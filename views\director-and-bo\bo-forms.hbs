<main class="">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="form-group">
                        <div class="alert alert-warning" role="alert">
                            <p>
                                <i class="fa fa-warning mr-2"></i><span class="font-weight-bold text-uppercase">Important!</span>
                            </p>
                            <p class="mt-2">
                                The information you see is based on the previous BVI legislation and Beneficial Owner
                                identification requirements. Those requirements have been replaced by new legislative
                                provisions which came into effect on 2 January 2025. The Beneficial Owner details
                                will be shown here in early June for you to confirm the Beneficial Ownership for your
                                entity, based on the current BVI legislation. You will be asked at that time to confirm
                                that the details we hold are correct, or to contact us with any changes.
                            </p>
                            <p>
                            The current legislation is set out in the BVI Business Companies Act (Revised 2020) (as
                            amended), the Limited Partnership Act (Revised 2020) (as amended), the Trustee Act
                            (Revised 2020) (as amended) and the BVI Business Companies and Limited Partnerships
                            (Beneficial Ownership) Regulations, 2024.
                            </p>
                        </div>
                    </div>
                    <h4 class="mt-3">Entity Name: <B>{{entityName}}</B></h4>
                    <br>
                    
                    {{#if directorsWithMissingValues}}
                        <input id="missingDirBoData" type="text" readonly hidden value="true">
                        {{> director-and-bo/missing-data-modal dataRecords=directorsWithMissingValues }}
                    {{/if}}

                    {{#if individualEntries}}
                    <h5>Individual Beneficial Owners</h5>
                    <div class="table-responsive">
                        <table id="individual-bo-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-13-percent min-width-200" >Name</th>
                                    <th class="header-15-percent min-width-150" >Date of Birth</th>
                                    <th class="header-15-percent min-width-150" >Place of Birth</th>
                                    <th class="header-15-percent min-width-150" >Nationality</th>
                                    <th class="header-15-percent min-width-200" >Residential Address</th>
                                    <th class="header-12-percent min-width-150" >TIN or other identification reference
                                        number, if any</th>
                                    <th class="header-15-percent min-width-150" ></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each individualEntries}}
                                {{#if oldData}} 
                                <tr><td class="bg-white" colspan="7"></td></tr> 
                                <tr class="font-italic bg-clear-gray">
                                    <td>
                                        <small>
                                            {{oldData.Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>

                                    <td>
                                        <small>
                                            {{oldData.Nationality}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{wrapText oldData.ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.TIN}}
                                        </small>
                                    </td>
                                    <td class="text-center">
                                        Previous Record
                                    </td>
                                </tr>
                                {{/if}}

                                <!-- NEW DATA -->
                                <tr>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{Name}}

                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>

                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{Nationality}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{wrapText ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{TIN}}
                                        </small>

                                    </td>
                                    <td class="text-center">
                                        {{#ifCond showInfoQuestion '===' true}}
                                        {{#if oldData}} <small><b>Updated Record</b></small><br><br> {{/if}}
                                        <div id="showInfo-{{UniqueRelationId}}">
                                            <label>Is the information correct?</label>
                                            <div class="d-flex flex-row  justify-content-around">
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnYes-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnYes-{{UniqueRelationId}}">
                                                        <small>YES {{Status}}</small>
                                                    </label>
                                                </div>
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnNo-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" value="NO"
                                                        data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnNo-{{UniqueRelationId}}">
                                                        <small>NO</small>
                                                    </label>
                                                </div>
                                            </div>

                                        </div>
                                        {{/ifCond}}

                                        {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                        <div>
                                            <span><small><b>CONFIRMED</b></small></span>
                                        </div>

                                        {{/ifCond}}

                                        {{#if canConfirm}}
                                        <button
                                            data-id="{{UniqueRelationId}}" id="confirmDataBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 confirm-btn confirmInformation {{#if
                                            showInfoQuestion}} hide-element{{/if}}">
                                            <small>Confirm</small>
                                        </button>
                                        {{/if}}

                                        {{#if canUpdate}}
                                        <button data-id="{{UniqueRelationId}}" data-type="{{../type}}"
                                            id="updateBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 request-update-btn requestUpdate{{#if
                                            showInfoQuestion}} hide-element{{/if}}"
                                            data-missing-values="{{hasMissingValues}}"
                                            >
                                            <small>Request Update</small>
                                        </button>
                                        {{/if}}


                                        {{#if showHistory}}
                                        <button
                                            data-type="{{lastChange.changeType}}" data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr> 
                                {{#if oldData}}
                                    <tr><td class="bg-white" colspan="7"></td></tr> 
                                {{/if}}

                                {{else}}
                                <tr>
                                    <td colspan="7">No records found</td>
                                </tr>
                                {{/each}}

                            </tbody>
                        </table>
                    </div>
                    <br>
                    <br>
                    {{/if}}



                    {{#if hasCorporateEntries}}
                    <h5>Corporate Beneficial Owners</h5>

                    {{#if boCorpEntriesGpt2}}
                    <div class="table-responsive">
                        <table id="company-bo1-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-13-percent min-width-200" >Name</th>
                                    <th class="header-15-percent min-width-150" >Incorporation Number</th>
                                    <th class="header-15-percent min-width-150" >Date of Incorporation</th>
                                    <th class="header-15-percent min-width-150" >Address</th>
                                    <th class="header-15-percent min-width-150" >Country of Formation</th>
                                    <th class="header-12-percent min-width-150" >TIN or other identification reference
                                        number, if any</th>
                                    <th class="header-15-percent min-width-150" ></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each boCorpEntriesGpt2}}
                                {{#if oldData}} 
                                    <tr><td class="bg-white" colspan="7"></td></tr> 
                                    <tr class="bg-clear-gray font-italic">
                                        <td>
                                            <small>
                                                {{oldData.Name}}
                                            </small>
                                        </td>
                                        <td>
                                            <small>
                                                {{oldData.BoDirIncorporationNumber}}
                                            </small>
                                        </td>
                                        <td>
                                            <small>
                                                {{oldData.DateOfBirthOrIncorp}}
                                            </small>
                                        </td>
                                        <td>
                                            <small>
                                                {{wrapText oldData.ResidentialOrRegisteredAddress}}
                                            </small>
                                        </td>
                                        <td>
                                            <small>
                                                {{oldData.PlaceOfBirthOrIncorp}}
                                            </small>
                                        </td>
                                        <td>
                                            <small>
                                                {{oldData.TIN}}
                                            </small>
                                        </td>

                                        <td class="text-center">
                                            Previous Record
                                        </td>
                                    </tr>
                                {{/if}}

                                <!-- NEW DATA -->
                                <tr class="bg-white">
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{BoDirIncorporationNumber}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{wrapText ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{TIN}}
                                        </small>
                                    </td>

                                    <td class="text-center">
                                        {{#ifCond showInfoQuestion '===' true}}
                                        {{#if oldData}} <small><b>Updated Record</b></small><br><br> {{/if}}
                                        <div id="showInfo-{{UniqueRelationId}}">
                                            <label>Is the information correct?</label>
                                            <div class="d-flex flex-row  justify-content-around">
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnYes-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnYes-{{UniqueRelationId}}">
                                                        <small>YES {{Status}}</small>
                                                    </label>
                                                </div>
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnNo-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" value="NO"
                                                        data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnNo-{{UniqueRelationId}}">
                                                        <small>NO</small>
                                                    </label>
                                                </div>
                                            </div>

                                        </div>
                                        {{/ifCond}}

                                        {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                        <div>
                                            <span><small><b>CONFIRMED</b></small></span>
                                        </div>

                                        {{/ifCond}}

                                        {{#if canConfirm}}
                                        <button data-id="{{UniqueRelationId}}" id="confirmDataBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 confirm-btn confirmInformation {{#if
                                            showInfoQuestion}} hide-element{{/if}}">
                                            <small>Confirm</small>
                                        </button>
                                        {{/if}}

                                        {{#if canUpdate}}
                                        <button data-id="{{UniqueRelationId}}" data-type="{{../type}}"
                                            id="updateBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 request-update-btn requestUpdate {{#if
                                            showInfoQuestion}} hide-element{{/if}}"
                                            data-missing-values="{{hasMissingValues}}">
                                            <small>Request Update</small>
                                        </button>
                                        {{/if}}


                                        {{#if showHistory}}
                                        <button
                                            data-type="{{lastChange.changeType}}" data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{#if oldData}}
                                    <tr><td class="bg-white" colspan="7"></td></tr> 
                                {{/if}}

                                {{else}}
                                <tr>
                                    <td colspan="7">No records found</td>
                                </tr>
                                {{/each}}

                            </tbody>
                        </table>
                    </div>
                    <br>
                    <br>
                    {{/if}}

                    {{#if boCorpEntriesGpt3}}
                    <div class="table-responsive">
                        <table id="company-bo2-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-13-percent min-width-200" >Name</th>
                                    <th class="header-10-percent min-width-150" >Incorporation Number</th>
                                    <th class="header-10-percent min-width-150" >Date of Incorporation</th>
                                    <th class="header-10-percent min-width-150" >Address</th>
                                    <th class="header-10-percent min-width-150" >Country of Formation</th>
                                    <th class="header-10-percent min-width-150" >Name of Regulator</th>
                                    <th class="header-10-percent min-width-150" >Jurisdiction of Regulator</th>
                                    <th class="header-12-percent min-width-150" >TIN or other identification reference
                                        number, if any</th>
                                    <th class="header-15-percent min-width-150" ></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each boCorpEntriesGpt3}}
                                {{#if oldData}}
                                <tr><td class="bg-white" colspan="9"></td></tr> 
                                <tr class="bg-clear-gray font-italic">
                                    <td>
                                        <small>
                                            {{oldData.Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.BoDirIncorporationNumber}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{wrapText oldData.ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.NameOfRegulator}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.JurisdictionOfRegulationOrSovereignState}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.TIN}}
                                        </small>
                                    </td>

                                    <td class="text-center">
                                        Previous Record
                                    </td>
                                </tr>
                                {{/if}}

                                <!-- NEW DATA -->
                                <tr class="bg-white">
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{BoDirIncorporationNumber}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{wrapText ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{NameOfRegulator}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{JurisdictionOfRegulationOrSovereignState}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{TIN}}
                                        </small>
                                    </td>

                                    <td class="text-center">
                                        {{#ifCond showInfoQuestion '===' true}}
                                        {{#if oldData}} <small><b>Updated Record</b></small><br> <br> {{/if}}
                                        <div id="showInfo-{{UniqueRelationId}}">
                                            <label>Is the information correct?</label>
                                            <div class="d-flex flex-row  justify-content-around">
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnYes-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnYes-{{UniqueRelationId}}">
                                                        <small>YES {{Status}}</small>
                                                    </label>
                                                </div>
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnNo-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" value="NO"
                                                        data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnNo-{{UniqueRelationId}}">
                                                        <small>NO</small>
                                                    </label>
                                                </div>
                                            </div>

                                        </div>
                                        {{/ifCond}}

                                        {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                        <div>
                                            <span><small><b>CONFIRMED</b></small></span>
                                        </div>

                                        {{/ifCond}}

                                        {{#if canConfirm}}
                                        <button data-id="{{UniqueRelationId}}" id="confirmDataBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 confirm-btn confirmInformation {{#if
                                            showInfoQuestion}} hide-element{{/if}}">
                                            <small>Confirm</small>
                                        </button>
                                        {{/if}}

                                        {{#if canUpdate}}
                                        <button data-id="{{UniqueRelationId}}" data-type="{{../type}}"
                                            id="updateBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 request-update-btn requestUpdate {{#if
                                            showInfoQuestion}} hide-element {{/if}}"
                                            data-missing-values="{{hasMissingValues}}">
                                            <small>Request Update</small>
                                        </button>
                                        {{/if}}


                                        {{#if showHistory}}
                                        <button
                                            data-type="{{lastChange.changeType}}" data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{#if oldData}}
                                    <tr><td class="bg-white" colspan="9"></td></tr> 
                                {{/if}}
                                {{else}}
                                <tr>
                                    <td colspan="9">No records found</td>
                                </tr>
                                {{/each}}

                            </tbody>
                        </table>
                    </div>
                    <br>
                    <br>
                    {{/if}}

                    {{#if boCorpEntriesGpt4}}
                    <div class="table-responsive">
                        <table id="company-bo3-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-13-percent min-width-200" >Name</th>
                                    <th class="header-10-percent min-width-150" >Incorporation Number</th>
                                    <th class="header-10-percent min-width-150" >Date of Incorporation</th>
                                    <th class="header-15-percent min-width-150" >Address</th>
                                    <th class="header-15-percent min-width-150" >Country of Formation</th>
                                    <th class="header-10-percent min-width-150" >Sovereign State</th>
                                    <th class="header-12-percent min-width-150" >TIN or other identification reference
                                        number, if any</th>
                                    <th class="header-15-percent min-width-150" ></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each boCorpEntriesGpt4}}

                                {{#if oldData}}
                                <tr><td class="bg-white" colspan="8"></td></tr> 
                                <tr class="bg-clear-gray font-italic">
                                    <td>
                                        <small>
                                            {{oldData.Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.BoDirIncorporationNumber}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{wrapText oldData.ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.PlaceOfBirthOrIncorp}}

                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.JurisdictionOfRegulationOrSovereignState}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.TIN}}
                                        </small>
                                    </td>

                                    <td class="text-center">
                                        Previous Record
                                    </td>
                                </tr>
                                {{/if}}


                                <tr class="bg-clear-gray {{#if oldData}} font-weight-bold {{/if}}" >
                                    <td>
                                        <small>
                                            {{Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{BoDirIncorporationNumber}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{wrapText ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{JurisdictionOfRegulationOrSovereignState}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{TIN}}
                                        </small>
                                    </td>

                                    <td class="text-center">
                                        {{#ifCond showInfoQuestion '===' true}}
                                        {{#if oldData}} <small><b>Updated Record</b></small><br><br> {{/if}}
                                        <div id="showInfo-{{UniqueRelationId}}">

                                            <label>Is the information correct?</label>
                                            <div class="d-flex flex-row  justify-content-around">
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnYes-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnYes-{{UniqueRelationId}}">
                                                        <small>YES {{Status}}</small>
                                                    </label>
                                                </div>
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnNo-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" value="NO"
                                                        data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnNo-{{UniqueRelationId}}">
                                                        <small>NO</small>
                                                    </label>
                                                </div>
                                            </div>

                                        </div>
                                        {{/ifCond}}

                                        {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                        <div>
                                            <span><small><b>CONFIRMED</b></small></span>
                                        </div>

                                        {{/ifCond}}

                                        {{#if canConfirm}}
                                        <button data-id="{{UniqueRelationId}}" id="confirmDataBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 confirm-btn confirmInformation {{#if
                                            showInfoQuestion}} hide-element {{/if}}">
                                            <small>Confirm</small>
                                        </button>
                                        {{/if}}

                                        {{#if canUpdate}}
                                        <button data-id="{{UniqueRelationId}}" data-type="{{../type}}"
                                            id="updateBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 request-update-btn requestUpdate {{#if
                                            showInfoQuestion}} hide-element {{/if}}"
                                            data-missing-values="{{hasMissingValues}}">
                                            <small>Request Update</small>
                                        </button>
                                        {{/if}}


                                        {{#if showHistory}}
                                        <button
                                            data-type="{{lastChange.changeType}}" data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{#if oldData}}
                                    <tr><td class="bg-white" colspan="8"></td></tr> 
                                {{/if}}
                                {{else}}
                                <tr>
                                    <td colspan="8">No records found</td>
                                </tr>
                                {{/each}}

                            </tbody>
                        </table>
                    </div>
                    <br>
                    <br>
                    {{/if}}

                    {{#if boCorpEntriesGpt5}}
                    <div class="table-responsive">
                        <table id="company-bo4-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-13-percent min-width-200" >Name</th>
                                    <th class="header-10-percent min-width-150" >Incorporation Number</th>
                                    <th class="header-10-percent min-width-150" >Date of Incorporation</th>
                                    <th class="header-10-percent min-width-150" >Address</th>
                                    <th class="header-10-percent min-width-150" >Country of Formation</th>
                                    <th class="header-10-percent min-width-150" >Stock Exchange</th>
                                    <th class="header-10-percent min-width-150" >Stock Code</th>
                                    <th class="header-12-percent min-width-150" >TIN or other identification reference
                                        number, if any</th>
                                    <th class="header-15-percent min-width-150" ></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each boCorpEntriesGpt5}}
                                {{#if oldData}}
                                <tr><td class="bg-white" colspan="9"></td></tr> 
                                <tr class="bg-clear-gray font-italic">
                                    <td>
                                        <small>
                                            {{oldData.Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.BoDirIncorporationNumber}}

                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.DateOfBirthOrIncorp}}

                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{wrapText oldData.ResidentialOrRegisteredAddress}}

                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.StockExchange}}
                                        </small>
                                    </td>
                                    <td>

                                        <small>
                                            {{oldData.StockCode}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.TIN}}
                                        </small>
                                    </td>

                                    <td class="text-center">
                                        Previous Record
                                    </td>
                                </tr>
                                {{/if}}

                                <!-- NEW DATA -->
                                <tr class="bg-white">
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{BoDirIncorporationNumber}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{wrapText ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{StockExchange}}
                                        </small>
                                    </td>
                                    <td>

                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{StockCode}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{TIN}}
                                        </small>
                                    </td>

                                    <td class="text-center">
                                        {{#ifCond showInfoQuestion '===' true}}
                                        {{#if oldData}} <small><b>Updated Record</b></small><br><br> {{/if}}
                                        <div id="showInfo-{{UniqueRelationId}}">

                                            <label>Is the information correct?</label>
                                            <div class="d-flex flex-row  justify-content-around">
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnYes-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnYes-{{UniqueRelationId}}">
                                                        <small>YES {{Status}}</small>
                                                    </label>
                                                </div>
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnNo-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" value="NO"
                                                        data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnNo-{{UniqueRelationId}}">
                                                        <small>NO</small>
                                                    </label>
                                                </div>
                                            </div>

                                        </div>
                                        {{/ifCond}}

                                        {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                        <div>
                                            <span><small><b>CONFIRMED</b></small></span>
                                        </div>

                                        {{/ifCond}}

                                        {{#if canConfirm}}
                                        <button data-id="{{UniqueRelationId}}" id="confirmDataBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 confirm-btn confirmInformation {{#if
                                            showInfoQuestion}} hide-element {{/if}}">
                                            <small>Confirm</small>
                                        </button>
                                        {{/if}}

                                        {{#if canUpdate}}
                                        <button data-id="{{UniqueRelationId}}" data-type="{{../type}}"
                                            id="updateBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 request-update-btn requestUpdate {{#if
                                            showInfoQuestion}} hide-element {{/if}}"
                                            data-missing-values="{{hasMissingValues}}">
                                            <small>Request Update</small>
                                        </button>
                                        {{/if}}


                                        {{#if showHistory}}
                                        <button
                                            data-type="{{lastChange.changeType}}" data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{#if oldData}}
                                    <tr><td class="bg-white" colspan="9"></td></tr> 
                                {{/if}}
                                {{else}}
                                <tr>
                                    <td colspan="9">No records found</td>
                                </tr>
                                {{/each}}

                            </tbody>
                        </table>
                    </div>
                    <br>
                    <br>
                    {{/if}}

                    {{#if boCorpEntriesGpt6}}
                    <div class="table-responsive">
                        <table id="company-bo1-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-13-percent min-width-200" >Name</th>
                                    <th class="header-15-percent min-width-150" >Incorporation Number</th>
                                    <th class="header-15-percent min-width-150" >Date of Incorporation</th>
                                    <th class="header-15-percent min-width-150" >Address</th>
                                    <th class="header-15-percent min-width-150" >Country of Formation</th>
                                    <th class="header-12-percent min-width-150" >TIN or other identification reference
                                        number, if any</th>
                                    <th class="header-15-percent min-width-150" ></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each boCorpEntriesGpt6}}
                                {{#if oldData}}
                                <tr><td class="bg-white" colspan="9"></td></tr> 
                                <tr class="bg-clear-gray font-italic">
                                    <td>
                                        <small>
                                            {{oldData.Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.BoDirIncorporationNumber}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{wrapText oldData.ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.TIN}}
                                        </small>
                                    </td>

                                    <td class="text-center">
                                        Previous Record
                                    </td>
                                </tr>
                                {{/if}}

                                <!-- NEW DATA -->
                                <tr class="bg-white">
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{BoDirIncorporationNumber}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{wrapText ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{TIN}}
                                        </small>
                                    </td>

                                    <td class="text-center">
                                        {{#ifCond showInfoQuestion '===' true}}

                                        <div id="showInfo-{{UniqueRelationId}}">
                                            {{#if oldData}} <small><b>Updated Record</b></small><br><br> {{/if}}
                                            <label>Is the information correct?</label>
                                            <div class="d-flex flex-row  justify-content-around">
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnYes-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnYes-{{UniqueRelationId}}">
                                                        <small>YES {{Status}}</small>
                                                    </label>
                                                </div>
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnNo-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" value="NO"
                                                        data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnNo-{{UniqueRelationId}}">
                                                        <small>NO</small>
                                                    </label>
                                                </div>
                                            </div>

                                        </div>
                                        {{/ifCond}}

                                        {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                        <div>
                                            <span><small><b>CONFIRMED</b></small></span>
                                        </div>

                                        {{/ifCond}}

                                        {{#if canConfirm}}
                                        <button data-id="{{UniqueRelationId}}" id="confirmDataBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 confirm-btn confirmInformation {{#if
                                            showInfoQuestion}} hide-element {{/if}}">
                                            <small>Confirm</small>
                                        </button>
                                        {{/if}}

                                        {{#if canUpdate}}
                                        <button data-id="{{UniqueRelationId}}" data-type="{{../type}}"
                                            id="updateBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 request-update-btn requestUpdate {{#if
                                            showInfoQuestion}} hide-element {{/if}}"
                                            data-missing-values="{{hasMissingValues}}">
                                            <small>Request Update</small>
                                        </button>
                                        {{/if}}


                                        {{#if showHistory}}
                                        <button
                                            data-type="{{lastChange.changeType}}" data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{#if oldData}}
                                    <tr><td class="bg-white" colspan="7"></td></tr> 
                                {{/if}}
                                {{else}}
                                <tr>
                                    <td colspan="7">No records found</td>
                                </tr>
                                {{/each}}

                            </tbody>
                        </table>
                    </div>
                    <br>
                    <br>
                    {{/if}}
                    {{/if}}

                    {{#unless individualEntries}}
                    {{#unless hasCorporateEntries}}
                    <p>
                        Please contact us for further assistance.
                    </p>
                    {{/unless}}
                    {{/unless}}

                    <br>

                    <div>
                        <a href="/masterclients/{{masterClientCode}}/director-and-bo"
                            class="btn btn-secondary waves-effect waves-light width-xl">
                            Back
                        </a>

                        {{#unless individualEntries}}
                            {{#unless hasCorporateEntries}}
                            <button class="btn solid  btn-primary" data-type="{{type}}"
                                id="requestAssistanceBtn">
                                Request Assistance
                            </button>
                            {{/unless}}
                        {{/unless}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/director-and-bo/requestupdatepopup.precompiled.js"></script>
<script type="text/javascript" src="/templates/director-and-bo/requestupdatelog.precompiled.js"></script>
<script type="text/javascript" src="/views-js/director-and-bo/bo-forms.js"></script>