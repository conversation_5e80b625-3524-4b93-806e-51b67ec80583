<main class="">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h4 class="mb-3"><strong>Member Details</strong></h4>
                    {{#if isIndividual}}
                    <div class="table-responsive mb-3">
                        <table id="individual-member-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-20-percent min-width-100">Name
                                        {{> director-and-members/required-info-icon field="SHName" missingValues=missingValues}}
                                    </th>
                                    <th class="header-10-percent min-width-100">Former Name</th>
                                    <th class="header-20-percent min-width-100">Address
                                        {{> director-and-members/required-info-icon field="MFRAAddress" missingValues=missingValues}}
                                    </th>
                                    <th class="header-10-percent min-width-100">Date of Birth
                                        {{> director-and-members/required-info-icon field="MFDateOfBirth" missingValues=missingValues}}
                                    </th>
                                    <th class="header-10-percent min-width-100">Place of Birth
                                        {{> director-and-members/required-info-icon field="MFBirthCountry" missingValues=missingValues}}
                                    </th>
                                    <th class="header-10-percent min-width-100">Nationality
                                        {{> director-and-members/required-info-icon field="MFNationality" missingValues=missingValues}}
                                    </th>
                                    <th class="header-10-percent min-width-100">Date Entered
                                        {{> director-and-members/required-info-icon field="dateEntered" missingValues=missingValues}}
                                    </th>
                                    <th class="header-10-percent min-width-100">Date Ceased</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{memberDetails.SHName}}</td>
                                    <td>{{memberDetails.shareholderProfile.MFFormerName}}</td>
                                    <td>{{memberDetails.shareholderProfile.MFRAAddress}}</td>
                                    <td>{{formatDate memberDetails.shareholderProfile.MFDateOfBirth "YYYY-MM-DD"}}</td>
                                    <td>{{memberDetails.shareholderProfile.MFBirthCountry}}</td>
                                    <td>{{memberDetails.shareholderProfile.MFNationality}}</td>
                                    <td>{{formatDate memberDetails.dateEntered "YYYY-MM-DD"}}</td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    {{/if}}
                    {{#if isCorporate}}
                    <div class="table-responsive">
                        <table id="corporate-member-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>Name
                                        {{> director-and-members/required-info-icon field="SHName" missingValues=missingValues}}
                                    </th>
                                    <th>Corporate Number
                                        {{> director-and-members/required-info-icon field="MFIncropNr" missingValues=missingValues}}
                                    </th>
                                    <th>Address
                                        {{> director-and-members/required-info-icon field="MFROAddress" missingValues=missingValues}}
                                    </th>
                                    <th>Incorporation Date
                                        {{> director-and-members/required-info-icon field="MFIncorpDate" missingValues=missingValues}}
                                    </th>
                                    <th>Incorporation Place
                                        {{> director-and-members/required-info-icon field="MFIncorpCountry" missingValues=missingValues}}
                                    </th>
                                    <th>Date Entered
                                        {{> director-and-members/required-info-icon field="dateEntered" missingValues=missingValues}}
                                    </th>
                                    <th>Date Ceased</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{memberDetails.SHName}}</td>
                                    <td>{{memberDetails.shareholderProfile.MFIncropNr}}</td>
                                    <td>{{memberDetails.shareholderProfile.MFROAddress}}</td>
                                    <td>{{formatDate memberDetails.shareholderProfile.MFIncorpDate "YYYY-MM-DD"}}</td>
                                    <td>{{memberDetails.shareholderProfile.MFIncorpCountry}}</td>
                                    <td>{{formatDate memberDetails.dateEntered "YYYY-MM-DD"}}</td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    {{/if}}
                    {{#if showShareDetails}}
                    <h4 class="my-3"><strong>Share Details</strong></h4>
                    <div class="table-responsive mb-3">
                        <table id="shares-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>Share Classification</th>
                                    <th>Certification Number</th>
                                    <th>Number of Shares</th>
                                    <th>Voting Rights</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each shareDetails}}
                                <tr>
                                    <td>{{ShareClassName}}</td>
                                    <td>{{SHCertNr}}</td>
                                    <td>{{NrOfShares}}</td>
                                    <td>
                                        {{#ifEquals SHVotingRights "Non-Voting"}}
                                        No
                                        {{else}}
                                        Yes
                                        {{/ifEquals}}
                                    </td>
                                    <td>{{formatDate ShareIssueDate "YYYY-MM-DD"}}</td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    {{/if}}
                    {{#if hasNominators}}
                    <h4 class="mb-3"><strong>Nominator Details</strong></h4>
                    {{#if individualNominators}}
                    <div class="table-responsive mb-3">
                        <table id="individual-nominator-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-20-percent min-width-100">Name</th>
                                    <th class="header-10-percent min-width-100">Former Name</th>
                                    <th class="header-20-percent min-width-100">Address
                                        {{> director-and-members/required-info-icon field="MFRAAddress" missingValues=missingValuesNominators}}
                                    </th>
                                    <th class="header-10-percent min-width-100">Date of Birth
                                        {{> director-and-members/required-info-icon field="MFDateOfBirth" missingValues=missingValuesNominators}}
                                    </th>
                                    <th class="header-10-percent min-width-100">Place of Birth
                                        {{> director-and-members/required-info-icon field="MFBirthCountry" missingValues=missingValuesNominators}}
                                    </th>
                                    <th class="header-10-percent min-width-100">Nationality
                                        {{> director-and-members/required-info-icon field="MFNationality" missingValues=missingValuesNominators}}
                                    </th>
                                    <th class="header-10-percent min-width-100">Date Entered</th>
                                    <th class="header-10-percent min-width-100">Date Ceased</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each individualNominators}}
                                <tr>
                                    <td>{{MemberName}}</td>
                                    <td>{{MFFormerName}}</td>
                                    <td>{{MFRAAddress}}</td>
                                    <td>{{formatDate MFDateOfBirth "YYYY-MM-DD"}}</td>
                                    <td>{{MFBirthCountry}}</td>
                                    <td>{{MFNationality}}</td>
                                    <td>{{formatDate MemberDateStart "YYYY-MM-DD"}}</td>
                                    <td></td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    {{/if}}
                    {{#if corporateNominators}}
                    <div class="table-responsive mb-3">
                        <table id="corporate-nominator-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Corporate Number
                                        {{> director-and-members/required-info-icon field="MFIncropNr" missingValues=missingValuesNominators}}
                                    </th>
                                    <th>Address
                                        {{> director-and-members/required-info-icon field="MFROAddress" missingValues=missingValuesNominators}}
                                    </th>
                                    <th>Incorporation Date
                                        {{> director-and-members/required-info-icon field="MFIncorpDate" missingValues=missingValuesNominators}}
                                    </th>
                                    <th>Incorporation Place
                                        {{> director-and-members/required-info-icon field="MFIncorpCountry" missingValues=missingValuesNominators}}
                                    </th>
                                    <th>Date Entered</th>
                                    <th>Date Ceased</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each corporateNominators}}
                                <tr>
                                    <td>{{MemberName}}</td>
                                    <td>{{MFIncropNr}}</td>
                                    <td>{{MFROAddress}}</td>
                                    <td>{{formatDate MFIncorpDate "YYYY-MM-DD"}}</td>
                                    <td>{{MFIncorpCountry}}</td>
                                    <td>{{formatDate MemberDateStart "YYYY-MM-DD"}}</td>
                                    <td></td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    {{/if}}
                    {{/if}}
                    <!-- Verification Questions -->
                    <div class="mb-4 mt-4">
                        <!-- Nominee Arrangement Question - Only shown if no nominee information in VP -->
                        <table class="table" id="verification-question-table">
                            <tbody>
                                {{#unless isNominee}}
                                <tr id="nominee-row">
                                    <th class="w-50 font-weight-normal">Are the Shares held under a nominee arrangement?</th>
                                    <td class="w-50">
                                        <div>
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input type="radio" id="nominee-yes" name="nominee-arrangement"
                                                    class="custom-control-input" value="yes" {{#if
                                                    hasNomineeArrangement}}checked{{/if}} {{#if
                                                    isConfirmed}}disabled{{/if}}>
                                                <label class="custom-control-label" for="nominee-yes">Yes</label>
                                            </div>
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input type="radio" id="nominee-no" name="nominee-arrangement"
                                                    class="custom-control-input" value="no" {{#ifEquals
                                                    hasNomineeArrangement false}}checked{{/ifEquals}} {{#if
                                                    isConfirmed}}disabled{{/if}}>
                                                <label class="custom-control-label" for="nominee-no">No</label>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {{/unless}}
                                {{#unless missingAnyValue}}
                                {{#unless isConfirmed}}
                                <tr id="is-correct-row">
                                    <th scope="row"><strong class="font-weight-bold text-dark">Is the information displayed above accurate and complete?</strong></th>
                                    <td>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" id="information-correct-yes" name="information-correct"
                                                class="custom-control-input" value="yes">
                                            <label class="custom-control-label"
                                                for="information-correct-yes">Yes</label>
                                        </div>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" id="information-correct-no" name="information-correct"
                                                class="custom-control-input" value="no">
                                            <label class="custom-control-label" for="information-correct-no">No</label>
                                        </div>
                                    </td>
                                </tr>
                                {{/unless}}
                                {{/unless}}
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <a href="/masterclients/{{masterClientCode}}/director-and-members/{{company.code}}/members"
                            class="btn btn-secondary waves-effect waves-light width-xl">
                            Back
                        </a>
                        {{#if showConfirmButton}}
                        <button id="confirmBtn"
                            class="btn solid royal-blue width-xl ml-2 hide-element" data-id="{{memberDetails.SHUniqueNr}}">Confirm</button>
                        {{/if}}
                        <button
                            type="button"
                            id="requestUpdateBtn"
                            data-id="{{memberDetails.SHUniqueNr}}"
                            class="btn solid royal-blue width-xl ml-2 hide-element"
                            data-missing-values="{{#if missingAnyValue}}true{{else}}false{{/if}}"
                            data-is-confirmed="{{#if isConfirmed}}true{{else}}false{{/if}}"
                            data-is-nominee="{{#if isNominee}}true{{else}}false{{/if}}"
                        >Request Update</button>
                    </div>
                </div> <!-- end card-box-->
            </div> <!-- end col -->
        </div> <!-- end row -->
    </div> <!-- end container-fluid -->
</main>

<!-- Include the Handlebars template for request update log -->
<script type="text/javascript" src="/templates/director-and-members/requestupdatelog.precompiled.js"></script>

<!-- Include the request update popup template -->
<script type="text/javascript" src="/templates/director-and-members/requestupdatepopup.precompiled.js"></script>

<!-- Include the member details form JavaScript -->
<script src="/views-js/director-and-members/member-details-forms.js"></script>