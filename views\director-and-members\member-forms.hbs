<main class="">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="form-group">
                        {{#if hasInformation}}
                            {{#if hasShareholders}}
                            <div class="alert alert-blue py-3 px-4">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fa fa-info-circle fa-lg mr-2"></i>
                                    <strong>Actions</strong>
                                </div>
                                <ul class="mb-0 pl-4 list-disc">
                                    <li class="list-item">
                                       To check the company’s member information, please click the "Show more to confirm" button. This will display all the details, and you’ll be asked to confirm if there is a nominee arrangement.
                                    </li>
                                    <li class="mt-2" class="list-item">
                                        By confirming this information, you agree that Trident Trust can process your data and submit the require information to the BVI Registry, as required by law.
                                    </li>
                                    <li class="mt-2" class="list-item">
                                        As part of this process, a private filing of the Register of Members will be submitted, and we anticipate that the nature of the associated voting rights is contained in the Memorandum and Articles of Association ("M&A"). If you’d like to make this filing public, or if your M&A doesn’t include details about the nature of associated voting rights, please contact your usual Trident representative for assistance.
                                    </li>
                                </ul>
                            </div>
                            {{/if}}
                            {{#if stockInformation}}
                                <div class="alert alert-blue py-3 px-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fa fa-info-circle fa-lg mr-2"></i>
                                        <strong>Actions</strong>
                                    </div>
                                    <ul class="mb-0 pl-4 list-disc">
                                        <li class="list-item">
                                            According to our records, your company is registered as listed company and falls into exemption for filing of Register of Members. Please verify the company’s stock information and confirm.
                                        </li>
                                        <li class="mt-2" class="list-item">
                                            By confirming this information, you agree that Trident Trust can process your data and submit the require information to the BVI Registry, as required by law.
                                        </li>
                                    </ul>
                                </div>
                            {{/if}}
                            {{#if mutualFundInformation}}
                                <div class="alert alert-blue py-3 px-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fa fa-info-circle fa-lg mr-2"></i>
                                        <strong>Actions</strong>
                                    </div>
                                    <ul class="mb-0 pl-4 list-disc">
                                        <li class="list-item">
                                            According to our records, your company is registered as mutual fund and falls into exemption for filing of Register of Members. Please verify the company’s mutual fund information and confirm.
                                        </li>
                                        <li class="mt-2" class="list-item">
                                            By confirming this information, you agree that Trident Trust can process your data and submit the require information to the BVI Registry, as required by law.
                                        </li>
                                    </ul>
                                </div>
                            {{/if}}
                        {{else}}
                            <div class="alert alert-warning py-3 px-4">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fa fa-warning fa-lg mr-2"></i>
                                    <strong>Actions</strong>
                                </div>
                                <ul class="mb-0 pl-4 list-disc">
                                    <li class="list-item">If you are unable to locate the entity’s Shareholder below, Please click the “Request Assistance” and your usual Trident representative shall be in touch to assist.</li>
                                </ul>
                            </div>
                        {{/if}}
                    </div>
                    <br>
                    {{#if membersWithMissingValues}}
                    <input id="missingDirMemberData" type="text" readonly hidden value="true">
                    {{> director-and-members/missing-data-modal dataRecords=membersWithMissingValues type="Member(s)"}}
                    {{/if}}

                    {{#if individualMembers}}
                    <h4 class="mb-3"><strong>Individual Member</strong></h4>
                    <div class="table-responsive">
                        <table id="individual-member-table" class="table table-sm table-striped table-equal-width mb-0">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Date Entered as Member</th>
                                    <th>Date Ceased as Member</th>
                                    <th>Status</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each individualMembers}}
                                <tr>
                                    <td>{{SHName}}</td>
                                    <td>{{formatDate ShareIssueDate "YYYY-MM-DD"}}</td>
                                    <td></td>
                                    <td>
                                        {{#if hasMissingValues}}
                                            MISSING INFORMATION
                                        {{else if confirmedAndUpdated}}
                                            CONFIRMED
                                        {{else if lastChange.status}}
                                            {{lastChange.status}}
                                        {{else}}
                                            CONFIRMATION REQUIRED
                                        {{/if}}
                                    </td>
                                    <td class="text-right">
                                        {{#if hasMissingValues}}
                                            <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{SHUniqueNr}}">Show More</a>
                                        {{else if lastChange.status}}
                                            {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn"
                                                    data-id="{{SHUniqueNr}}">Show More</a>
                                            {{else}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn"
                                                    data-id="{{SHUniqueNr}}">Show More to Confirm</a>
                                            {{/ifCond}}
                                        {{else}}
                                        <a href="#" class="btn solid royal-blue width-xl show-more-btn"
                                            data-id="{{SHUniqueNr}}">Show More to Confirm</a>
                                        {{/if}}

                                        {{#if showHistory}}
                                        <button data-type="{{lastChange.changeType}}"
                                            data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary ml-2 showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <br>
                    {{/if}}

                    {{#if corporateMembers}}
                    <h4 class="mb-3"><strong>Corporate Member</strong></h4>
                    <div class="table-responsive">
                        <table id="corporate-member-table" class="table table-sm table-equal-width table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Date Entered as Member</th>
                                    <th>Date Ceased as Member</th>
                                    <th>Status</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each corporateMembers}}
                                <tr>
                                    <td>{{SHName}}</td>
                                    <td>{{formatDate ShareIssueDate "YYYY-MM-DD"}}</td>
                                    <td></td>
                                    <td>
                                        {{#if hasMissingValues}}
                                            MISSING INFORMATION
                                        {{else if confirmedAndUpdated}}
                                            CONFIRMED
                                        {{else if lastChange.status}}
                                            {{lastChange.status}}
                                        {{else}}
                                            CONFIRMATION REQUIRED
                                        {{/if}}
                                    </td>
                                    <td class="text-right">
                                        {{#if hasMissingValues}}
                                            <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{SHUniqueNr}}">Show More</a>
                                        {{else if lastChange.status}}
                                            {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn"
                                                    data-id="{{SHUniqueNr}}">Show More</a>
                                            {{else}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn"
                                                    data-id="{{SHUniqueNr}}">Show More to Confirm</a>
                                            {{/ifCond}}
                                        {{else}}
                                        <a href="#" class="btn solid royal-blue width-xl show-more-btn"
                                            data-id="{{SHUniqueNr}}">Show More to Confirm</a>
                                        {{/if}}

                                        {{#if showHistory}}
                                        <button data-type="{{lastChange.changeType}}"
                                            data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary ml-2 showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <br>
                    {{/if}}

                    {{#if jointMembers}}
                    <h4 class="mb-3"><strong>Joint Shareholder</strong></h4>
                    <div class="table-responsive">
                        <table id="corporate-member-table" class="table table-sm table-equal-width table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Date Entered as Member</th>
                                    <th>Date Ceased as Member</th>
                                    <th>Status</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each jointMembers}}
                                <tr>
                                    <td>{{SHName}}</td>
                                    <td>{{formatDate ShareIssueDate "YYYY-MM-DD"}}</td>
                                    <td></td>
                                    <td>
                                        {{#if hasMissingValues}}
                                            MISSING INFORMATION
                                        {{else if isConfirmedInHistory}}
                                            CONFIRMED
                                        {{else if lastChange.status}}
                                            {{lastChange.status}}
                                        {{else}}
                                            CONFIRMATION REQUIRED
                                        {{/if}}
                                    </td>
                                    <td class="text-right">
                                        {{#if lastChange.status}}
                                            {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn"
                                                    data-type="joint" data-certnr="{{SHCertNr}}">Show More</a>
                                            {{else if hasMissingValues}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn"
                                                    data-type="joint" data-certnr="{{SHCertNr}}">Show More</a>
                                            {{else}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn"
                                                    data-type="joint" data-certnr="{{SHCertNr}}">Show More to Confirm</a>
                                            {{/ifCond}}
                                        {{else}}
                                        <a href="#" class="btn solid royal-blue width-xl show-more-btn"
                                            data-type="joint" data-certnr="{{SHCertNr}}">Show More to Confirm</a>
                                        {{/if}}

                                        {{#if showHistory}}
                                        <button data-type="{{lastChange.changeType}}"
                                            data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary ml-2 showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <br>
                    {{/if}}

                    {{#if stockInformation}}
                    <h4 class="mb-3"><strong>Stock Information</strong></h4>
                    <div class="table-responsive">
                        <table id="stock-information-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-14-percent">
                                        Stock Exchange Name
                                        {{> director-and-members/required-info-icon field="STXName" missingValues=missingStockFields}}
                                        </th>
                                    <th class="header-14-percent">
                                        Ticker Symbol
                                        {{> director-and-members/required-info-icon field="STXTicker" missingValues=missingStockFields}}
                                        </th>
                                    <th class="header-14-percent">
                                        Jurisdiction of Stock Exchange
                                        {{> director-and-members/required-info-icon field="STXJurisdiction" missingValues=missingStockFields}}
                                        </th>
                                    <th class="header-14-percent">
                                        Name of Stock Exchange Regulator
                                        {{> director-and-members/required-info-icon field="STXRegulator" missingValues=missingStockFields}}
                                        </th>
                                    <th class="header-8-percent">
                                        Date of Listing
                                        {{> director-and-members/required-info-icon field="STXListingDate" missingValues=missingStockFields}}
                                        </th>
                                    <th class="header-8-percent">Status</th>
                                    {{#unless missingStockFields}}
                                    <th class="header-12-percent">Is this information correct?</th>
                                    {{/unless}}
                                    <th class="header-16-percent"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{stockInformation.STXName}}</td>
                                    <td>{{stockInformation.STXTicker}}</td>
                                    <td>{{stockInformation.STXJurisdiction}}</td>
                                    <td>{{stockInformation.STXRegulator}}</td>
                                    <td>{{formatDate stockInformation.STXListingDate "YYYY-MM-DD"}}</td>
                                    <td>
                                        {{#if missingStockFields}}
                                            MISSING INFORMATION
                                        {{else if stockInformation.confirmedAndUpdated}}
                                            CONFIRMED
                                        {{else if stockInformation.lastChange.status}}
                                            {{stockInformation.lastChange.status}}
                                        {{else}}
                                            CONFIRMATION REQUIRED
                                        {{/if}}
                                    </td>
                                    {{#unless missingStockFields}}
                                    <td>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" id="stock-info-yes" name="stock-info-correct"
                                            {{#if stockInformation.isConfirmed}}disabled checked{{/if}}
                                            class="custom-control-input stock-radio" value="yes">
                                            <label class="custom-control-label" for="stock-info-yes">Yes</label>
                                        </div>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" id="stock-info-no" name="stock-info-correct"
                                            {{#if stockInformation.isConfirmed}}disabled{{/if}}
                                            class="custom-control-input stock-radio" value="no">
                                            <label class="custom-control-label" for="stock-info-no">No</label>
                                        </div>
                                    </td>
                                    {{/unless}}
                                    <td class="text-right">
                                        {{#if stockInformation.showHistory}}
                                            <button
                                                data-type="{{stockInformation.lastChange.changeType}}"
                                                data-reason="{{stockInformation.lastChange.changeReason}}"
                                                class="btn btn-sm solid width-md btn-secondary ml-2 showLastChange"
                                            >
                                                <small>View History</small>
                                            </button>
                                        {{/if}}
                                        <button
                                            type="button"
                                            class="btn solid royal-blue btn-sm hide-element confirm-stock"
                                            data-id="{{company.code}}"
                                        >
                                            Confirm
                                        </button>
                                        <button
                                            type="button"
                                            id="requestUpdateBtn"
                                            class="btn solid royal-blue btn-sm hide-element request-stock-update"
                                            data-id="{{company.code}}"
                                            data-missing-values="{{#if missingStockFields}}true{{else}}false{{/if}}"
                                            data-is-confirmed="{{#if stockInformation.isConfirmed}}true{{else}}false{{/if}}"
                                        >
                                            Request Update
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <br>
                    {{/if}}

                    {{#if mutualFundInformation}}
                    <h4 class="mb-3"><strong>Mutual Fund Information</strong></h4>
                    <div class="table-responsive">
                        <table id="stock-information-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-24-percent">
                                        Licence Number
                                        {{> director-and-members/required-info-icon field="BusRegNr" missingValues=missingFundFields}}
                                        </th>
                                    <th class="header-20-percent">
                                        Licence Type
                                        {{> director-and-members/required-info-icon field="BusRegType" missingValues=missingFundFields}}
                                        </th>
                                    <th class="header-20-percent">
                                        Start Date
                                        {{> director-and-members/required-info-icon field="BusRegStartDate" missingValues=missingFundFields}}
                                        </th>
                                    <th class="header-8-percent">Status</th>
                                    {{#unless missingFundFields}}
                                    <th class="header-12-percent">Is this information correct?</th>
                                    {{/unless}}
                                    <th class="header-16-percent"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{mutualFundInformation.BusRegNr}}</td>
                                    <td>{{mutualFundInformation.BusRegType}}</td>
                                    <td>{{formatDate mutualFundInformation.BusRegStartDate "YYYY-MM-DD"}}</td>
                                    <td>
                                        {{#if missingFundFields}}
                                            MISSING INFORMATION
                                        {{else if mutualFundInformation.confirmedAndUpdated}}
                                            CONFIRMED
                                        {{else if mutualFundInformation.lastChange.status}}
                                            {{mutualFundInformation.lastChange.status}}
                                        {{else}}
                                            CONFIRMATION REQUIRED
                                        {{/if}}
                                    </td>
                                    {{#unless missingFundFields}}
                                    <td>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" id="fund-info-yes" name="fund-info-correct"
                                            {{#if mutualFundInformation.isConfirmed}}disabled checked{{/if}}
                                            class="custom-control-input fund-radio" value="yes">
                                            <label class="custom-control-label" for="fund-info-yes">Yes</label>
                                        </div>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" id="fund-info-no" name="fund-info-correct"
                                            {{#if mutualFundInformation.isConfirmed}}disabled{{/if}}
                                            class="custom-control-input fund-radio" value="no">
                                            <label class="custom-control-label" for="fund-info-no">No</label>
                                        </div>
                                    </td>
                                    {{/unless}}
                                    <td class="text-right">
                                        {{#if mutualFundInformation.showHistory}}
                                            <button
                                                    data-type="{{mutualFundInformation.lastChange.changeType}}"
                                                    data-reason="{{mutualFundInformation.lastChange.changeReason}}"
                                                    class="btn btn-sm solid width-md btn-secondary ml-2 showLastChange"
                                            >
                                                <small>View History</small>
                                            </button>
                                        {{/if}}
                                        <button 
                                            type="button"
                                            class="btn solid royal-blue btn-sm hide-element confirm-fund"
                                            data-id="{{company.code}}"
                                        >
                                            Confirm
                                        </button>
                                        <button 
                                            type="button"
                                            id="requestUpdateBtn"
                                            class="btn solid royal-blue btn-sm hide-element request-fund-update" 
                                            data-id="{{company.code}}" 
                                            data-missing-values="{{#if missingFundFields}}true{{else}}false{{/if}}"
                                            data-is-confirmed="{{#if mutualFundInformation.isConfirmed}}true{{else}}false{{/if}}"
                                        >
                                            Request Update
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <br>
                    {{/if}}

                    <br>

                    <div>
                        <a href="/masterclients/{{masterClientCode}}/director-and-members"
                            class="btn btn-secondary waves-effect waves-light width-xl">
                            Back
                        </a>

                        {{#unless hasInformation}}
                            <button class="btn solid  btn-primary" id="requestAssistanceBtn">
                                Request Assistance
                            </button>
                        {{/unless}}
                    </div>
                </div> <!-- end card-box-->
            </div> <!-- end col -->
        </div> <!-- end row -->
    </div> <!-- end container-fluid -->
</main>

<script type="text/javascript" src="/templates/director-and-members/requestupdatelog.precompiled.js"></script>
<script type="text/javascript" src="/templates/director-and-members/requestupdatepopup.precompiled.js"></script>
<script src="/views-js/director-and-members/member-forms.js"></script>