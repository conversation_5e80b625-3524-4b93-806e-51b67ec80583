const appInsights = require("applicationinsights");
appInsights.setup(process.env.APPLICATIONINSIGHTS_CONNECTION_STRING);
appInsights.start();

const createError = require('http-errors');
const express = require('express');
const redis = require("redis");
const path = require('path');
const cookieParser = require('cookie-parser');
const logger = require('morgan');
const session = require('express-session');
const fileupload = require("express-fileupload");
const redisStore = require('connect-redis')(session);
const hbs = require('express-handlebars');
const handlebars = require('handlebars');
const { allowInsecurePrototypeAccess } = require('@handlebars/allow-prototype-access')
const moment = require('moment');
const momentTimezone = require('moment-timezone');
const passport = require('passport');
const flash = require('connect-flash');
require('./config/passport')(passport);
require('express-async-errors');
const { COUNTRIES } = require('./utils/constants');
const { doubleCsrf } = require("csrf-csrf");
const csrfHeader = process.env.SECURE_COOKIE === true ? `${process.env.SUBSTANCE_APP_HOST}.x-csrf-token` : 'x-csrf-token';

const sqlDb = require('./models-sql');

let app = express();

const indexRouter = require('./routes/index');
const masterclientsRouter = require('./routes/masterclients');
const messagesRouter = require('./routes/messages');
const incorporateCompanyRouter = require('./routes/incorporate-company');
const companiesRouter = require('./routes/substance-companies');
const substanceEntryRouter = require('./routes/substance-entry');
const monitorRouter = require("./routes/monitor");
const idPalRouter = require("./routes/id-pal");
const financialReportRouter = require('./routes/financial-reports');
const companyRequestedFilesRouter = require('./routes/company-files');
const directorAndBoRouter = require('./routes/director-and-bo');
const directorAndMembersRouter = require('./routes/director-and-members');




//connect to database
//Set up mongoose connection
var mongoose = require('mongoose');
var mongoDB = process.env.MONGODB;
console.log('connection ' + mongoDB);
mongoose.connect(mongoDB);
mongoose.set('strictQuery', false);
var db = mongoose.connection;
db.on('error', console.error.bind(console, 'MongoDB connection error:'));

//connect to sql database
momentTimezone.tz.setDefault('UTC');
sqlDb.sequelize.authenticate().then(()=> {
  console.log("SQL connection has been established successfully.")
}).catch(err => {
  console.log("Unable to connect to the SQL database: ", err);
});


// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'hbs');
app.engine('hbs', hbs.engine({
  extname: 'hbs',
  handlebars: allowInsecurePrototypeAccess(handlebars),
  defaultView: 'default',
  layoutsDir: __dirname + '/views/layouts/',
  partialsDir: __dirname + '/views/partials/',
  helpers: {
    formatDate: function (date, format) {
      if (date) {
        return moment(date).utc().format(format);
      } else {
        return '';
      }
    },
    encodeURIComponent: function (text) {
      if (text) {
        return encodeURIComponent(text);
      } else {
        return '';
      }
    },
    partlyPeriodYes: function (activitySelected, partlySelected, options) {
      if (activitySelected && partlySelected) {
        return options.fn(this);
      }
      return options.inverse(this);
    },
    partlyPeriodNo: function (activitySelected, partlySelected, options) {
      if (activitySelected && partlySelected == false) {
        return options.fn(this);
      }
      return options.inverse(this);
    },
    renderValidationMessage: function (message, field) {
      if (field) {
        return new handlebars.SafeString('<p class="alert alert-danger"><a class="alert-danger" href="#' + field + '">' + message + '</a></p>');
      } else {
        return new handlebars.SafeString('<p class="alert alert-danger">' + message + '</p>');
      }
    },
    ifEquals: function (str1, str2, options) {
      return str1 === str2 ? options.fn(this) : options.inverse(this);
    },
    ifNotIn: function (element, list, options) {
      return list.indexOf(element) === -1 ? options.fn(this) : options.inverse(this);
    },
    ifContains: function (element, list, options) {
      if (list && list.length > 0){
        return list.indexOf(element) > -1 ? options.fn(this) : options.inverse(this);
      }
      else{
        return options.inverse(this);
      }

    },
    ifCond: function (v1, operator, v2, options) {
      switch (operator) {
        case '==':
          return (v1 == v2) ? options.fn(this) : options.inverse(this);
        case '===':
          return (v1 === v2) ? options.fn(this) : options.inverse(this);
        case '!=':
          return (v1 != v2) ? options.fn(this) : options.inverse(this);
        case '!==':
          return (v1 !== v2) ? options.fn(this) : options.inverse(this);
        case '<':
          return (v1 < v2) ? options.fn(this) : options.inverse(this);
        case '<=':
          return (v1 <= v2) ? options.fn(this) : options.inverse(this);
        case '>':
          return (v1 > v2) ? options.fn(this) : options.inverse(this);
        case '>=':
          return (v1 >= v2) ? options.fn(this) : options.inverse(this);
        case '&&':
          return (v1 && v2) ? options.fn(this) : options.inverse(this);
        case '||':
          return (v1 || v2) ? options.fn(this) : options.inverse(this);
        default:
          return options.inverse(this);
      }
    },
    concat: function () {
      let concatValue = "";
      //const options = arguments[arguments.length - 1];
      for (let i = 0; i < arguments.length - 1; ++i) {
        //Do your thing with each array element.
        if (typeof (arguments[i]) === "string" || typeof (arguments[i]) === "number") {
          concatValue = concatValue + arguments[i].toString();
        }
      }
      //Return your results...
      return concatValue;
    },
    ternary: function (condition, v1, v2) {
      if (condition) {
        return v1;
      } else {
        return v2;
      }
    },
    wrapText: function (text) {
      if(!text){
        return "";
      }
      const newText = text.replace(/[\^;]/g, "<br>");
      return new handlebars.SafeString(newText);
    },
    getCountryName: function(countryCode){
      if (!countryCode) {
        return "";
      }
      const country = COUNTRIES.find((country) => country.alpha_2_code === countryCode || country.alpha_3_code === countryCode);

      return new handlebars.SafeString(country?.name || countryCode);
    },
    decimalValue: function (dVal) {
      if (dVal) {
        return Number(dVal).toLocaleString("en", { minimumFractionDigits: 2 });
      } else {
        return 0.00;
      }
    },
    calculatePercentage: function (total = 0, percentage = 0) {
      let result = 0;
      if (percentage) {
        let calculate = ((total * percentage) / 100);
        result = total + calculate
      }
      return result.toLocaleString("en", { minimumFractionDigits: 2 });
    },
    add: function (a, b) {
      return a + b;
    }
  }
}));


var redisClient = redis.createClient(6380, process.env.REDIS_HOST,
  { auth_pass: process.env.REDIS_PASS, tls: { servername: process.env.REDIS_HOST } });

console.log('ENV ' + process.env.NODE_ENV)
app.use('/views-js', express.static(path.join(__dirname, 'views-js')))
app.use(logger('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(express.static(path.join(__dirname, 'public')));

const cookieSettings = {
  maxAge: 7200000,  //120 mins
  secure:  process.env.SECURE_COOKIE === 'true',
  httpOnly: true,
  sameSite: 'strict'
};

app.use('/masterclients/:masterclientcode/financial-reports/companies/:companyCode/import/load-file', fileupload());
app.use('/masterclients/:masterclientcode/company-files/:companyCode/:requestId/upload-file', fileupload());

//https://expressjs.com/en/advanced/best-practice-security.html

app.set('trust proxy', 1); // trust first proxy

app.use(session({
  secret: process.env.REDIS_SECRET,
  name: 'id',
  store: new redisStore({ client: redisClient }),
  resave: false,
  saveUninitialized: false,
  cookie: cookieSettings
}));
// Initialize Passport!  Also use passport.session() middleware, to support
// persistent login sessions (recommended).
app.use(passport.initialize());
app.use(passport.session());
app.use(flash());


// set CSRF-CSRF 
const { invalidCsrfTokenError, generateToken, doubleCsrfProtection } = doubleCsrf({
  getSecret: () => process.env.CSRF_SECRET,
  cookieName: csrfHeader,
  cookieOptions: cookieSettings,
  ignoredMethods: ["GET", "HEAD", "OPTIONS"],
  getTokenFromRequest: (req) => req.headers['x-csrf-token'] ? req.headers['x-csrf-token'] : req.body["csrf-token"],
});

app.use(cookieParser(process.env.COOKIES_SECRET));



// middleware to validate csrf token
app.use(doubleCsrfProtection);

// middleware to set the csrf token 
app.use(function (req, res, next) {
  res.setHeader('Cache-control', ['no-store', 'no-cache', 'must-revalidate']);
  res.setHeader('Pragma', 'no-cache');
  res.locals.appHost = process.env.SUBSTANCE_APP_HOST;

  const ignoredMethods = ["GET", "HEAD", "OPTIONS"];
  if (req.method === "GET") {

    const csrfToken = generateToken(req, res);

    if(!req.session.csrfToken) {
      req.session.csrfToken = csrfToken
    } 
    
    if (req.session.csrfToken !== csrfToken) {
      throw invalidCsrfTokenError;
    } else {
      res.setHeader('x-csrf-token', csrfToken);
      res.locals.csrfToken = csrfToken
    }
  }
  else if (!ignoredMethods.includes(req.method)) {
    const csrfToken = req.csrfToken();

    if(req.session.csrfToken !== csrfToken) {
      throw invalidCsrfTokenError;
    } else {
      res.setHeader('x-csrf-token', csrfToken);
      res.locals.csrfToken = csrfToken
    }

  }

  next();

});

 




app.use('/', indexRouter);
app.use('/substance/entry', substanceEntryRouter);
app.use('/masterclients/:masterclientcode/substance/companies', companiesRouter);
app.use('/masterclients/:masterclientcode/incorporate-company/', incorporateCompanyRouter);
app.use('/masterclients', masterclientsRouter);
app.use('/messages', messagesRouter);
app.use('/monitor', monitorRouter);
app.use('/idpal', idPalRouter);
app.use('/masterclients/:masterclientcode/financial-reports', financialReportRouter);
app.use('/masterclients/:masterclientcode/company-files', companyRequestedFilesRouter);
app.use('/masterclients/:masterclientcode/director-and-bo', directorAndBoRouter);
app.use('/masterclients/:masterclientcode/director-and-members', directorAndMembersRouter);

require('./routes/users')(app, passport);

// catch 404 and forward to error handler
app.use(function (req, res, next) {
  next(createError(404));
});


// error handler
// eslint-disable-next-line no-unused-vars
app.use(function (err, req, res, next) {
  // set locals, only providing error in development

  const isAjaxRequest = req.xhr || req.headers.accept.indexOf('json') > -1;

  if (err == invalidCsrfTokenError) {
    res.locals.message = process.env.DEVELOPMENT_ERRORS === 'true' ? 'Csrf validation error' : 'An error ocurred!';

    if (isAjaxRequest) {
      return req.logout(function (err) {
        if (err) {
          return res.status(401).json({ redirect: '/', message: res.locals.message });
        }
        req.session.destroy(function () {
          const csrfToken = generateToken(req, res, true);
          res.setHeader('x-csrf-token', csrfToken);
          res.locals.csrfToken = csrfToken;
          res.status(401).json({ redirect: '/',  message: res.locals.message });
        });
      });
    }
    else {
      return req.logout(function (err) {
        if (err) {
          return next(err)
        }
        req.session.destroy(function () {
          const csrfToken = generateToken(req, res, true);
          res.setHeader('x-csrf-token', csrfToken);
          res.locals.csrfToken = csrfToken;
          res.redirect("/");
        });
      });
    }

  } else {
    if (process.env.DEVELOPMENT_ERRORS === 'true') {
      res.locals.message = err.message;
      res.locals.error = req.app.get('env') === 'development' ? err : {};
    } else {
      res.locals.message = 'An error ocurred!';
      res.locals.error = {};
    }


    // render the error page
    res.status(err.status || 500);

  }

  res.render('error');
});


app.disable('x-powered-by');

module.exports = app;
