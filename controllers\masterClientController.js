const sqlDb = require('../models-sql');
const EntryModel = require('../models/entry').EntryModel;
const Company = require('../models/company').schema;
const FinancialReportModel = require('../models/financial-report');
const MasterClientCode = require('../models/masterClientCode');
const CompanyIncorporationModel = require('../models/clientIncorporation');
const MasterClientMessageModel = require('../models/master-client-message');
const RequestFileModel = require('../models/requestedFile');
const VpDirectorInfoModel = sqlDb.VpDirectorInfo;
const mem_DirectorsModel = sqlDb.mem_Directors;
const mem_EntitiesModel = sqlDb.mem_Entities;
const mem_ShareholdersModel = sqlDb.mem_Shareholders;
const { Op } = require("sequelize");
const sessionUtils = require('../utils/sessionUtils');
const { DIRMEMBER_STATUS } = require('../utils/directorAndMemberConstants');

exports.masterClientDashboard = async function (req, res, next) {
    try {

        let pendingRequestFilesCount = RequestFileModel.countDocuments({
            masterClientCode: req.params.masterclientcode, status: {"$in": ["NOT STARTED", "IN PROGRESS"]}});
            
        let pendingSubstanceRequestFilesCount = EntryModel.countDocuments({
            'company_data.masterclientcode': req.params.masterclientcode, status: "INFORMATION REQUEST"});


        let pendingFinancialReportRFCount = FinancialReportModel.countDocuments({
            'companyData.masterclientcode': req.params.masterclientcode, status: "INFORMATION REQUEST"
        });


        let hasSubstanceCompanies = Company.countDocuments({ "masterclientcode": req.params.masterclientcode, "substanceModule.active": true  });
        let hasFinancialReport = Company.countDocuments({ "masterclientcode": req.params.masterclientcode, "accountingRecordsModule.active": true });
        let dirBoCompanies = Company.find({ "masterclientcode": req.params.masterclientcode, "dirboModule.active": true }, { code: 1 });

        [pendingRequestFilesCount, pendingSubstanceRequestFilesCount, pendingFinancialReportRFCount, dirBoCompanies, hasSubstanceCompanies, hasFinancialReport] = await Promise.all([
            pendingRequestFilesCount,
            pendingSubstanceRequestFilesCount,
            pendingFinancialReportRFCount,
            dirBoCompanies,
            hasSubstanceCompanies,
            hasFinancialReport,
        ]);
        
        
        let mccVPDirectors = 0;
        let mccMemDirectors = 0;
        let mccMemEntities = 0;
        let mccMemShareholders = 0;
        
        let vpDirectorsDataReceived = 0;
        let hasUnconfirmedData = false;

        if (dirBoCompanies.length > 0){
            const codes = dirBoCompanies.map((c) => c.code);
            mccVPDirectors = await VpDirectorInfoModel.count({
                where: { CompanyNumber: codes }
            });
            mccMemDirectors = await mem_DirectorsModel.count({
                where: { EntityLegacyID: codes }
            });
            mccMemEntities = await mem_EntitiesModel.count({
                where: { EntityLegacyID: codes }
            });
            mccMemShareholders = await mem_ShareholdersModel.count({
                where: { EntityLegacyID: codes }
            });

            if (mccMemDirectors || mccMemEntities || mccMemShareholders){
                const unconfirmedDirectors = await mem_DirectorsModel.findAll({
                    where: {
                        EntityLegacyID: codes,
                        UniqueRelationID: {
                            [Op.notIn]: [
                                sqlDb.sequelize.literal(`(
                                    SELECT UniqueRelationID 
                                    FROM mem_DirectorsHistory 
                                    WHERE EntityLegacyID = mem_Directors.EntityLegacyID 
                                    AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                                )`)
                                ]
                        }
                    },
                    raw: true
                });
    
                // Check for mem_Shareholders where there is no confirmed history record
                const unconfirmedMembers = await mem_ShareholdersModel.findAll({
                    where: {
                        EntityLegacyID: codes,
                        UniqueRelationID: {
                            [Op.notIn]: [
                                sqlDb.sequelize.literal(`(
                                    SELECT UniqueRelationID 
                                    FROM mem_ShareholdersHistory 
                                    WHERE EntityLegacyID = mem_Shareholders.EntityLegacyID 
                                    AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                                )`)
                                ]
                                }
                    },
                    raw: true
                });
    
                // Check for mem_Entities where there is no confirmed history record and it has stock or mutual fund information
                const unconfirmedEntities = await mem_EntitiesModel.findAll({
                    where: {
                        EntityLegacyID: codes,
                        [Op.or]: [
                            { STXName: { [Op.ne]: null } },
                            { STXTicker: { [Op.ne]: null } },
                            { STXJurisdiction: { [Op.ne]: null } },
                            { STXRegulator: { [Op.ne]: null } },
                            { STXListingDate: { [Op.ne]: null } },
                            { BusRegNr: { [Op.ne]: null } },
                            { BusRegType: { [Op.ne]: null } },
                            { BusRegStartDate: { [Op.ne]: null } }
                        ],
                        [Op.and]: [
                            sqlDb.sequelize.literal(`NOT EXISTS (
                                SELECT 1
                                FROM mem_EntitiesStockHistory
                                WHERE EntityLegacyID = mem_Entities.EntityLegacyID
                                AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                            )`),
                            sqlDb.sequelize.literal(`NOT EXISTS (
                                SELECT 1
                                FROM mem_EntitiesMutualFundHistory
                                WHERE EntityLegacyID = mem_Entities.EntityLegacyID
                                AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                            )`)
                        ]
                    },
                    raw: true
                });

                hasUnconfirmedData = unconfirmedDirectors.length > 0 || unconfirmedMembers.length > 0 || unconfirmedEntities.length > 0;
            }

        }


        res.render('masterclient/dashboard', {
            title: 'Dashboard',
            masterclientcode: req.params.masterclientcode,
            user: req.user,
            messages: req.session.messages,
            pendingRequestFilesCount: pendingRequestFilesCount + pendingSubstanceRequestFilesCount + pendingFinancialReportRFCount,
            vpDirectorsDataReceived: vpDirectorsDataReceived,
            hasSubstanceCompanies: hasSubstanceCompanies && hasSubstanceCompanies > 0,
            hasDirectors: mccVPDirectors > 0,
            hasMemDirectors: mccMemDirectors > 0 || mccMemEntities > 0 || mccMemShareholders > 0,
            hasFinancialReport: hasFinancialReport && hasFinancialReport > 0,
            hasUnconfirmedData,
        });
    } catch (e) {
        return next(e);
    }
};

// Display list of all Companys.

exports.masterClientList = async function (req, res, next) {
    try{

        const data = await MasterClientCode.find({ 'owners': req.user.email.toLowerCase() }, ['code', 'hasFiles']);
        const mccCodes = data.length > 0 ? data.map((mcc) => mcc.code) : [];
        let unreadMessages = [];
        let readMessages = [];
        let result = []

        if (mccCodes.length > 0) {
            let companies = await Company.find({ masterclientcode: { $in: mccCodes } }, { code: 1, masterclientcode: 1 });

            const companyCodes = companies.length > 0 ? companies.map((c) => c.code) : [];

            const paymentCountPromise = CompanyIncorporationModel.aggregate([
                {
                    $match: {
                        'status': 'SUBMITTED',
                        'masterClientCode': { $in: mccCodes }
                    }
                },
                {
                    $group: {
                        _id: '$masterClientCode',
                        count: { $sum: 1 }
                    }
                }
            ]);

            const pendingRequestFilesCountPromise = RequestFileModel.aggregate([
                {
                    $match: {
                        'masterClientCode': { $in: mccCodes },
                        'status': { $in: ["NOT STARTED", "IN PROGRESS"] }
                    }
                },
                {
                    $group: {
                        _id: '$masterClientCode',
                        count: { $sum: 1 }
                    }
                }
            ]);

            const messageListPremise = MasterClientMessageModel.find({ masterClientCode: { $in: mccCodes } }, {
                '_id': 1,
                'messageId': 1,
                'openedAt': 1,
                'masterClientCode': 1,
                'important': 1,
            });

            let [paymentCountData, pendingRequestFilesCountData, messageList] = await Promise.all([
                paymentCountPromise,
                pendingRequestFilesCountPromise,
                messageListPremise
            ]);

            let unconfirmedCompanyCodes = [];
            if (companyCodes.length > 0) {
                const unconfirmedDirectors = await mem_DirectorsModel.findAll({
                    where: {
                        EntityLegacyID: companyCodes,
                        UniqueRelationID: {
                            [Op.notIn]: [
                                sqlDb.sequelize.literal(`(
                                    SELECT UniqueRelationID 
                                    FROM mem_DirectorsHistory 
                                    WHERE EntityLegacyID = mem_Directors.EntityLegacyID 
                                    AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                                )`)
                                ]
                        }
                    },
                    raw: true
                });
    
                // Check for mem_Shareholders where there is no confirmed history record
                const unconfirmedMembers = await mem_ShareholdersModel.findAll({
                    where: {
                        EntityLegacyID: companyCodes,
                        UniqueRelationID: {
                            [Op.notIn]: [
                                sqlDb.sequelize.literal(`(
                                    SELECT UniqueRelationID 
                                    FROM mem_ShareholdersHistory 
                                    WHERE EntityLegacyID = mem_Shareholders.EntityLegacyID 
                                    AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                                )`)
                                ]
                                }
                    },
                    raw: true
                });
    
                // Check for mem_Entities where there is no confirmed history record and it has stock or mutual fund information
                const unconfirmedEntities = await mem_EntitiesModel.findAll({
                    where: {
                        EntityLegacyID: companyCodes,
                        [Op.or]: [
                            { STXName: { [Op.ne]: null } },
                            { STXTicker: { [Op.ne]: null } },
                            { STXJurisdiction: { [Op.ne]: null } },
                            { STXRegulator: { [Op.ne]: null } },
                            { STXListingDate: { [Op.ne]: null } },
                            { BusRegNr: { [Op.ne]: null } },
                            { BusRegType: { [Op.ne]: null } },
                            { BusRegStartDate: { [Op.ne]: null } }
                        ],
                        [Op.and]: [
                            sqlDb.sequelize.literal(`NOT EXISTS (
                                SELECT 1
                                FROM mem_EntitiesStockHistory
                                WHERE EntityLegacyID = mem_Entities.EntityLegacyID
                                AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                            )`),
                            sqlDb.sequelize.literal(`NOT EXISTS (
                                SELECT 1
                                FROM mem_EntitiesMutualFundHistory
                                WHERE EntityLegacyID = mem_Entities.EntityLegacyID
                                AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                            )`)
                        ]
                    },
                    raw: true
                });

                unconfirmedCompanyCodes = [
                    ...new Set([
                        ...unconfirmedDirectors.map((d) => d.EntityLegacyID),
                        ...unconfirmedMembers.map((m) => m.EntityLegacyID),
                        ...unconfirmedEntities.map((e) => e.EntityLegacyID)
                    ])
                ];
            }
            
            const importantMessages = messageList.filter((m) => !m.openedAt && m.important);
            messageList.forEach((message) => {
                if (message.openedAt) {
                    unreadMessages.filter((m) => m !== message.messageId.toString());
                    readMessages.push(message.messageId.toString());
                } else {
                    if (!readMessages.includes(message.messageId.toString()) && !unreadMessages.includes(message.messageId.toString())) {
                        unreadMessages.push(message.messageId.toString());
                    }
                }
            });

            result = data.map((masterclient) => {
                const paymentCountItem = paymentCountData.find(item => item._id === masterclient.code);
                const pendingRequestFilesCountItem = pendingRequestFilesCountData.find(item => item._id === masterclient.code);
                const companiesByMcc = companies.filter((c) => c.masterclientcode === masterclient.code).map((c) => c.code);

                let showNotification = false;
                let hasUnconfirmedMemDirData = false;

                if(companiesByMcc.length > 0){
                    // If there are unconfirmed companies for this MCC, show the notification
                    if (unconfirmedCompanyCodes.some((c) => companiesByMcc.includes(c))) {
                        hasUnconfirmedMemDirData = true;
                    }
                }

                if (importantMessages.length > 0) {
                    showNotification = importantMessages.some((m) => m.masterClientCode === masterclient.code);
                }
                const paymentCount = paymentCountItem ? paymentCountItem.count : 0;
                const pendingRequestFilesCount = pendingRequestFilesCountItem ? pendingRequestFilesCountItem.count : 0;

                return {
                    code: masterclient.code,
                    pending_payments: paymentCount,
                    hasFiles: masterclient.hasFiles,
                    pendingRequestFilesCount: (pendingRequestFilesCount > 0),
                    showNotification: showNotification,
                    hasUnconfirmedMemDirData
                };
            });
        }

        req.session.messages = unreadMessages.length;
        res.render('masterclient/list', { title: 'Master Clients', masterclients: result, user: req.user, messages: req.session.messages });
    }
    catch (e) {
        console.log("error: ", e);
        return next(e);
    }
}


exports.ensureAuthenticated = function (req, res, next) {
    if ((req.user && req.session.id == req.user.sessionId) && req.session.auth2fa) {
        const sessData = req.session;
        //check if compancode in session is the same as the company code in the url
        if (!req.params.companyCode || req.params.companyCode == sessData.company.code) {
            next();
        } else {
            req.logout(function (err) {
                if (err) { return next(err) }
                req.session.destroy(function () {
                    // cannot access session here
                    sessionUtils.onSessionDestroyed(req, res);
                });
            });
        }
    } else if ((req.user && req.session.id == req.user.sessionId) && !req.session.auth2fa) {
        if (req.user.secret_2fa) {
            res.redirect('/users/2fa-code');
        } else {
            res.redirect('/users/2fa-setup');
        }
    } else {
        req.logout(function (err) {
            if (err) { return next(err) }
            req.session.destroy(function () {
                // cannot access session here
                sessionUtils.onSessionDestroyed(req, res);
            });
        });
    }
};

exports.validateMasterClient = async function (req, res, next) {
    try{
        const masterclient = await MasterClientCode.findOne({ 'code': req.params.masterclientcode });
        if (masterclient == null || masterclient.owners.indexOf(req.user.email.toLowerCase()) == -1) {
            let err = new Error('Masterclient not found');
            err.status = 404;
            return next(err);
        }
        return next();
    }catch(e){
        console.log(e);
        next(e);
    }

}
