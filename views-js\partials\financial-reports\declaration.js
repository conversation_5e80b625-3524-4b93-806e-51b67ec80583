$('#declarationRelationControl').change(function () {
    if ($(this).val() === 'Other') {
        $('#declarationRelationOtherRow').show('200');
    } else {
        $('#declarationRelationOtherRow').hide('200');
    }
});

$(document).ready(function () {
    let input = document.querySelector("#declarationPhoneNumberControl");
    window.intlTelInput(input, {
        hiddenInput: "fullDeclarationPhoneNumberControl",
        utilsScript: "/javascripts/libs/intlTelInput/utils.js",
    });
});