$(document).ready(function () {
    const missingDirMemberData = $("#missingDirMemberData").val();

    if (missingDirMemberData === "true") {
        $('#missingDataModal').modal('show')
    }

    // Initially hide both buttons for all sections
    $('.confirm-stock, .confirm-fund').hide();
    $('.request-stock-update, .request-fund-update').hide();

    // Handle stock information radio buttons
    $('.stock-radio').on('change', function () {
        const value = $(this).val();
        const tr = $(this).closest('tr');

        if (value === 'yes') {
            // If information is correct, show confirm button
            tr.find('.confirm-stock').show();
            tr.find('.request-stock-update').hide();
        } else if (value === 'no') {
            // If information is not correct, show request update button
            tr.find('.confirm-stock').hide();
            tr.find('.request-stock-update').show();
        }
    });

    // Handle mutual fund information radio buttons
    $('.fund-radio').on('change', function () {
        const value = $(this).val();
        const tr = $(this).closest('tr');

        if (value === 'yes') {
            // If information is correct, show confirm button
            tr.find('.confirm-fund').show();
            tr.find('.request-fund-update').hide();
        } else if (value === 'no') {
            // If information is not correct, show request update button
            tr.find('.confirm-fund').hide();
            tr.find('.request-fund-update').show();
        }
    });

    // Stock information confirm button click handler
    $('.confirm-stock').on('click', function () {
        // No need to pass ID for stock confirmations
        confirmInformation(null, 'stock');
    });

    // Mutual fund confirm button click handler
    $('.confirm-fund').on('click', function () {
        // No need to pass ID for fund confirmations
        confirmInformation(null, 'fund');
    });

    // Handlers for the request-stock-update and request-fund-update buttons
    $('.request-stock-update').on('click', function () {
        requestUpdate($(this).attr('data-missing-values'), 'stock');
    });

    $('.request-fund-update').on('click', function () {
        requestUpdate($(this).attr('data-missing-values'), 'fund');
    });

    // Show request update if missing values
    $('.request-stock-update[data-missing-values="true"]').show();
    $('.request-fund-update[data-missing-values="true"]').show();

    // Show request update if is confirmed
    $('.request-stock-update[data-is-confirmed="true"]').show();
    $('.request-fund-update[data-is-confirmed="true"]').show();
});

// Extract masterClientCode from URL path
const urlPath = window.location.pathname;
const pathParts = urlPath.split('/');
const masterClientCodeIndex = pathParts.indexOf('masterclients') + 1;
const masterClientCode = pathParts[masterClientCodeIndex];

async function requestUpdate(missingValues, type) {
    let template = Handlebars.templates.requestupdatepopup;
    let d = {
        type,
        hasMissingData: (missingValues === "true")
    };
    let html = template(d);

    // Get the URL parts to construct the correct endpoint URL
    const urlParts = window.location.pathname.split('/');
    const companyCode = urlParts[4];
    const redirectUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/members/`;
    let requestUpdateUrl;

    if (type === 'stock') {
        // Full URL for stock confirmation - no ID needed
        requestUpdateUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/request-stock-update`;
    } else if (type === 'fund') {
        // Full URL for fund confirmation - no ID needed
        requestUpdateUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/request-mutual-fund-update`;
    }

    Swal.fire(
        {
            title: "Are you sure you want to request an update?",
            icon: "warning",
            html: html,
            showCancelButton: !0,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Submit",
            focusConfirm: false,
            showLoaderOnConfirm: true,
            reverseButtons: true,
            preConfirm: async () => {
                const changeType = $('#changeType').val();
                const changeReason = $('#changeReason').val();
                if (!changeType || changeType === "") {
                    Swal.showValidationMessage('Please select an option')
                }
                return axios.post(requestUpdateUrl,
                    JSON.stringify({
                        changeType: changeType,
                        changeReason: changeReason,
                    }),
                    {
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                        },
                    }
                ).then(response => {
                    try {
                        return response.data
                    } catch (e) {
                        throw new Error(response.statusText)
                    }
                }).catch(error => {
                    if (error?.response?.data) {
                        return error.response.data
                    }
                    return { status: error.status || 500, error: error }

                });

            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then(function (result) {
            if (result.isConfirmed) {
                swal.showLoading();
                if (result.value.status === 200) {
                    Swal.fire({
                        title: 'Success',
                        html: `We have received your request to update the ${type} information. A Trident Trust Representative will be in touch shortly.`,
                        icon: 'success',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#3085d6'
                    }).then(() => {
                        // Redirect to the main/company overview screen
                        window.location.href = redirectUrl;
                    });
                } else if (result.value.status === 400 || result.value.status === 404) {
                    Swal.fire('Error', result.value.error, 'error');
                } else {
                    Swal.fire('Error', 'There was an error generating the request', 'error');
                }
            }
        })
}

function showLastChange(type, reason) {
    let template = Handlebars.templates.requestupdatelog;
    let d = {
        changeType: type,
        changeReason: reason
    };
    let html = template(d);

    Swal.fire({
        title: "Last request for update",
        icon: "info",
        html: html
    })
}

async function confirmInformation(id, type) {
    // Get the URL parts to construct the correct endpoint URL
    const urlParts = window.location.pathname.split('/');
    const companyCode = urlParts[4];
    let confirmUrl;

    if (type === 'stock') {
        // Full URL for stock confirmation - no ID needed
        confirmUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/confirm-stock`;
    } else if (type === 'fund') {
        // Full URL for fund confirmation - no ID needed
        confirmUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/confirm-fund`;
    }

    // Create request body based on type
    let requestBody = {
        companyCode: companyCode,
        type: type  // Indicate which type we're confirming
    };

    Swal.fire({
        title: '<h4><strong>Confirm</strong></h4>',
        html: 'Are you sure you want to confirm the information? <br><br> By confirming the information, please note that you have consented to us processing your data for the BVI Registry pursuant to the law requirements.',
        icon: "info",
        iconColor: '#e6b800',
        customClass: {
            icon: 'swal-icon-small',
            padding: '20px',
        },
        showCancelButton: true,
        confirmButtonText: 'Submit',
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        focusConfirm: false,
        showLoaderOnConfirm: true,
        reverseButtons: true,
        preConfirm: async () => {
            return axios.post(confirmUrl,
                requestBody,
                {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                }).then(response => {
                    try {
                        return response.data
                    } catch (e) {
                        throw new Error(response.statusText)
                    }
                }).catch(error => {
                    if (error?.response?.data) {
                        return error.response.data
                    }
                    return { status: error.status || 500, error: error }
                });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then(function (result) {
        if (result.isConfirmed) {
            swal.showLoading();
            if (result.value.status === 200) {
                Swal.fire('Success', result.value.message, 'success').then(() => {
                    location.reload();
                });
            } else if (result.value.status === 400 || result.value.status === 404) {
                Swal.fire('Error', result.value.error, 'error');
            } else {
                Swal.fire('Error', 'There was an error with the confirmation request', 'error');
            }
        }
    })
}

$('.showLastChange').on('click', function () {
    showLastChange($(this).attr('data-type'), $(this).attr('data-reason'))
})

$('#requestAssistanceBtn').on('click', (event) => {
    $(this).prop('disabled', true);
    event.preventDefault();

    Swal.fire({
        title: "Are you sure you want to request assistance?",
        icon: "info",
        iconColor: "#F59E0C",
        showCancelButton: !0,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Confirm",
        focusConfirm: false,
        showLoaderOnConfirm: true,
        reverseButtons: true,
        backdrop: true,
        preConfirm: async () => {
            return axios.post(`${window.location.href}/request-assistance`, {},
                {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                }).then(response => {
                    try {
                        return response.data
                    } catch (e) {
                        throw new Error(response.statusText)
                    }
                }).catch(error => {
                    if (error?.response?.data) {
                        return error.response.data
                    }
                    return { status: error.status || 500, error: error }

                });

        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then(function (result) {
        if (result.isConfirmed) {
            swal.showLoading();
            if (result.value.status === 200) {
                Swal.fire('Success', result.value.message, 'success').then(() => {
                    location.reload();
                });
            } else if (result.value.status === 400 || result.value.status === 404) {
                Swal.fire('Error', result.value.error, 'error');
                $("#requestAssistanceBtn").prop('disabled', false);
            } else {
                $("#requestAssistanceBtn").prop('disabled', false);
                Swal.fire('Error', 'There was an error generating the request', 'error');

            }
        } else {
            $("#requestAssistanceBtn").prop('disabled', false);
        }
    })
})

$('.show-more-btn').on('click', function (e) {
    e.preventDefault();
    const memberId = $(this).data('id');
    if ($(this).data('type') === 'joint') {
        const certNr = $(this).data('certnr');
        window.location.href = `${window.location.href}/joint/${certNr}/details`;
        return;
    }

    window.location.href = `${window.location.href}/${memberId}/details`;
})
