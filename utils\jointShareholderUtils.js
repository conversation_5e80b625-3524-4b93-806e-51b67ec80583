exports.groupKeysByMatchingSHUniqueNr = (obj) => {
  const groups = {};
  const processedKeys = new Set();

  for (const key in obj) {
    if (processedKeys.has(key)) continue;

    const currentArray = obj[key];
    const matchingKeys = [key];

    // Find all other keys with the same SHUniqueNr values
    for (const otherKey in obj) {
      if (otherKey === key || processedKeys.has(otherKey)) continue;

      const otherArray = obj[otherKey];

      // Check if arrays have the same SHUniqueNr values
      if (this.arraysHaveSameSHUniqueNr(currentArray, otherArray)) {
        matchingKeys.push(otherKey);
        processedKeys.add(otherKey);
      }
    }

    // Create the grouped key name
    const groupedKey = matchingKeys.sort((a, b) => Number(a) - Number(b)).join('-');
    groups[groupedKey] = currentArray;
    processedKeys.add(key);
  }

  return groups;
}

exports.arraysHaveSameSHUniqueNr = (arr1, arr2) => {
  if (arr1.length !== arr2.length) return false;

  const values1 = arr1.map(item => item.SHUniqueNr).sort();
  const values2 = arr2.map(item => item.SHUniqueNr).sort();

  return values1.every((value, index) => value === values2[index]);
}