const sqlDb = require('../models-sql'); 
const Company = require('../models/company').schema;
const MasterClientCode = require('../models/masterClientCode');
const mem_DirectorsModel = sqlDb.mem_Directors;
const mem_ShareholdersModel = sqlDb.mem_Shareholders;
const mem_ShareholdersHistoryModel = sqlDb.mem_ShareholdersHistory;
const mem_DirectorsHistoryModel = sqlDb.mem_DirectorsHistory;
const mem_MemberProfilesModel = sqlDb.mem_MemberProfiles;
const mem_EntitiesModel = sqlDb.mem_Entities;
const mem_MemberProfilesHistoryModel = sqlDb.mem_MemberProfilesHistory;
const mem_EntitiesStockHistory = sqlDb.mem_EntitiesStockHistory;
const mem_EntitiesMutualFundHistory = sqlDb.mem_EntitiesMutualFundHistory;
const VpDirectorInfoHistoryModel = sqlDb.VpDirectorInfoHistory;
const { groupKeysByMatchingSHUniqueNr } = require('../utils/jointShareholderUtils');
const MailController = require('../controllers/mailController');
const MailFormatter = require('../controllers/mailFormatController');
const moment = require('moment');
const httpConstants = require('http2').constants;
const { 
    TYPE_OF_DIRECTOR_DIR,
    TYPE_OF_MEMBER,
    DIRECTOR_REQUIRED_FIELDS,
    MEMBER_REQUIRED_FIELDS,
    DIRMEMBER_STATUS,
} = require('../utils/directorAndMemberConstants');
const { Op } = require("sequelize");

// Display list of all Companys.
exports.listDirectorAndMemberCompanies = async function (req, res, next) {
    try{
        const mcc = await MasterClientCode.findOne({ 'code': req.params.masterclientcode});

        if (mcc == null || mcc.owners.indexOf(req.user.email.toLowerCase()) == -1) {
            const err = new Error('Masterclient not found');
            err.status = 404;
            return next(err);
        }

        let companies = [];
        
        try {
            // First, try to get companies from MongoDB
            let companyfilter = { 
                'masterclientcode': req.params.masterclientcode,
                'dirboModule.active': true
            };

            if (req.query.filterCompanyName && req.query.filterCompanyName.length > 2) {
                companyfilter['name'] = { $regex: req.query.filterCompanyName, $options: 'i' };
            }
            if (req.query.filterIncorporationCode && req.query.filterIncorporationCode.length > 2) {
                companyfilter['incorporationcode'] = { $regex: req.query.filterIncorporationCode, $options: 'i' };
            }

            let mongoCompanies = await Company.find(
                companyfilter, 
                { code: 1, name: 1, incorporationcode:1, masterclientcode:1 }
            ).sort({"name":1});
            
            // Convert to the format expected by the view
            companies = mongoCompanies.map(company => {
                return {
                    company: company,
                    unconfirmedDataMember: false,
                    unconfirmedDataDirector: false,
                    hasDirectorsAndMembers: false,
                };
            });

            // Check if any companies have directors or members
            if (companies.length > 0) {
                // Get all company codes
                const companyCodes = companies.map(c => c.company.code);
                const vpEntities = await mem_EntitiesModel.findAll({
                    where: {
                        EntityLegacyID: companyCodes,
                    },
                    raw: true
                });

                const vpEntityCodes = vpEntities.map(e => e.EntityLegacyID);

                companies = companies.filter(c => vpEntityCodes.includes(c.company.code));
                
                // Check for mem_Directors where there is no confirmed history record
                const unconfirmedDirectors = await mem_DirectorsModel.findAll({
                    where: {
                        EntityLegacyID: companyCodes,
                        UniqueRelationID: {
                            [Op.notIn]: [
                                sqlDb.sequelize.literal(`(
                                    SELECT UniqueRelationID 
                                    FROM mem_DirectorsHistory 
                                    WHERE EntityLegacyID = mem_Directors.EntityLegacyID 
                                    AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                                )`)
                                ]
                        }
                    },
                    raw: true
                });

                // Check for mem_Shareholders where there is no confirmed history record
                const unconfirmedMembers = await mem_ShareholdersModel.findAll({
                    where: {
                        EntityLegacyID: companyCodes,
                        UniqueRelationID: {
                            [Op.notIn]: [
                                sqlDb.sequelize.literal(`(
                                    SELECT UniqueRelationID 
                                    FROM mem_ShareholdersHistory 
                                    WHERE EntityLegacyID = mem_Shareholders.EntityLegacyID 
                                    AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                                )`)
                                ]
                                }
                    },
                    raw: true
                });

                // Check for mem_Entities where there is no confirmed history record and it has stock or mutual fund information
                const unconfirmedEntities = await mem_EntitiesModel.findAll({
                    where: {
                        EntityLegacyID: companyCodes,
                        [Op.or]: [
                            { STXName: { [Op.ne]: null } },
                            { STXTicker: { [Op.ne]: null } },
                            { STXJurisdiction: { [Op.ne]: null } },
                            { STXRegulator: { [Op.ne]: null } },
                            { STXListingDate: { [Op.ne]: null } },
                            { BusRegNr: { [Op.ne]: null } },
                            { BusRegType: { [Op.ne]: null } },
                            { BusRegStartDate: { [Op.ne]: null } }
                        ],
                        [Op.and]: [
                            sqlDb.sequelize.literal(`NOT EXISTS (
                                SELECT 1
                                FROM mem_EntitiesStockHistory
                                WHERE EntityLegacyID = mem_Entities.EntityLegacyID
                                AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                            )`),
                            sqlDb.sequelize.literal(`NOT EXISTS (
                                SELECT 1
                                FROM mem_EntitiesMutualFundHistory
                                WHERE EntityLegacyID = mem_Entities.EntityLegacyID
                                AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                            )`)
                        ]
                    },
                    raw: true
                });

                // Set flags for each company
                companies.forEach(company => {
                    company.unconfirmedDataDirector = unconfirmedDirectors.some(d => d.EntityLegacyID === company.company.code);
                    company.unconfirmedDataMember = unconfirmedMembers.some(m => m.EntityLegacyID === company.company.code) || unconfirmedEntities.some(e => e.EntityLegacyID === company.company.code);
                });
                
                // Get all companies with directors in a single query
                let companiesWithDirectors = await mem_DirectorsModel.findAll({
                    attributes: ['EntityLegacyID'],
                    where: {
                        EntityLegacyID: companyCodes,
                    },
                    group: ['EntityLegacyID'],
                    raw: true
                });
                
                // Get all companies with members in a single query
                const companiesWithMembers = await mem_ShareholdersModel.findAll({
                    attributes: ['EntityLegacyID'],
                    where: {
                        EntityLegacyID: companyCodes,
                    },
                    group: ['EntityLegacyID'],
                    raw: true
                });
            
                // Create lookup sets for faster checking
                const directorCompanyCodes = new Set(companiesWithDirectors.map(c => c.EntityLegacyID));
                const memberCompanyCodes = new Set(companiesWithMembers.map(c => c.EntityLegacyID));
                
                // Set flags for each company
                companies.forEach(company => {
                    company.hasDirectors = directorCompanyCodes.has(company.company.code);
                    company.hasMembers = memberCompanyCodes.has(company.company.code);
                });
            }
        } catch (err) {
            console.error("Error fetching companies from MongoDB:", err);
        }

        return res.render('director-and-members/companies', { 
            title: 'Companies', 
            masterClientCode: req.params.masterclientcode, 
            companies: companies, 
            user: req.user,
            messages: req.session.messages,
            hideMembers: process.env.HIDE_MEMBERS === 'true'
        });

    } catch(e) {
        console.error("Error in listDirectorAndMemberCompanies:", e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};


async function getDirectorCompanyData(code, mcc, user){
    //this will test the current user has access to the mcc, and thus allowed to the company
    try {
        let masterclient = await MasterClientCode.findOne({ 'code': mcc });
        if (masterclient == null || masterclient.owners?.indexOf(user.toLowerCase()) == -1) {
            return { status: httpConstants.HTTP_STATUS_NOT_FOUND, error: 'Masterclient not found'}
        }
        const company = await Company.findOne({ code: code, masterclientcode: mcc });
        return company || { status: httpConstants.HTTP_STATUS_NOT_FOUND, error: 'Company not found' }

    }catch(e){
        console.error("Error getting director company data: ", e);
        return { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, error: 'Internal server error' }
    }

}


exports.getDirectorEntries = async function (req, res, next) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if(company?.error){
            const err = new Error(company.error);
            err.status = company.status;
            return next(err);
        }

        let directorsWithMissingValues = [];
        let entityName = company.name;
        let directors = [];

        // Get directors from mem_Directors table
        let memDirectors = await mem_DirectorsModel.findAll({
            where: {
                EntityLegacyID: company.code,
            },
            include: [{
                model: mem_MemberProfilesModel,
                as: 'directorProfile',
                required: false
            }
        ],
            raw: false
        });
        
        if (memDirectors && memDirectors.length > 0) {
            entityName = memDirectors[0].EntityName;
        }

        // Process records from mem_Directors
        if (memDirectors && memDirectors.length > 0) {
            // Filter out directors with populated DirToDate (Cessation Date)
            directors = memDirectors.filter(director => !director.DirToDate);
        }

        if (directors.length > 0) {
            // Get history from mem_DirectorsHistory table
            const directorIds = directors.map(d => d.UniqueRelationID);

            const historyLogs = await mem_DirectorsHistoryModel.findAll({
                where: { 
                    EntityLegacyID: company.code, 
                    UniqueRelationID: directorIds,
                },
                include: [{
                    model: mem_MemberProfilesHistoryModel,
                    as: 'directorProfileHistory',
                    required: false
                }
            ],
                raw: true
            });

            // Get the history logs for these directors from old VpDirectorInfoHistory table
            let oldHistoryLogs = [];
            try {
                oldHistoryLogs = await VpDirectorInfoHistoryModel.findAll({
                    where: { 
                        CompanyNumber: company.code,
                        RelationType: "Director",
                        UniqueRelationId: directors.map(d => d.PreviousUniqueRelationId).filter(id => id)
                    },
                    raw: true
                });
            } catch (error) {
                console.error("Error fetching old history logs:", error);
                oldHistoryLogs = [];
            }

            if (historyLogs && historyLogs.length > 0) {
                historyLogs.sort((a, b) => b.Id - a.Id);
            }

            if (oldHistoryLogs && oldHistoryLogs.length > 0) {
                oldHistoryLogs.sort((a, b) => b.Id - a.Id);
            }

            directors = directors.map((director) => {
                director.canUpdate = true;
                director.canConfirm = true;
                director.showHistory = false;
                director.showInfoQuestion = true;
                
                // Check if this director was confirmed in the old VP history logs
                // AND there are no records in the new history database
                const hasNewHistoryRecords = historyLogs.some(
                    log => log.UniqueRelationID === director.UniqueRelationID
                );
                
                director.isConfirmedInHistory = !hasNewHistoryRecords && oldHistoryLogs && oldHistoryLogs.some(log => 
                    log.UniqueRelationId === director.PreviousUniqueRelationId && 
                    log.Status === DIRMEMBER_STATUS.CONFIRMED
                );
                
                // Check mem_DirectorsHistory logs
                const entryLogs = historyLogs.filter(
                    (r) => r.UniqueRelationID === director.UniqueRelationID && r.RelationType === director.RelationType
                );

                let latestHistory = null;
                
                if (entryLogs.length > 0) {
                    entryLogs.sort((a, b) => b.Id - a.Id);
                    const lastLogCreated = entryLogs[0];
                    latestHistory = lastLogCreated;
                    
                    if (lastLogCreated.Status !== DIRMEMBER_STATUS.RECEIVED && 
                        lastLogCreated.Status !== DIRMEMBER_STATUS.INITIAL && 
                        lastLogCreated.Status !== DIRMEMBER_STATUS.REFRESHED) {
                        director.showInfoQuestion = false;
                        director.canConfirm = false;
                    } else if (lastLogCreated.Status == DIRMEMBER_STATUS.RECEIVED || 
                               lastLogCreated.Status == DIRMEMBER_STATUS.REFRESHED) {
                        const confirmedOrUpdateRequestLogs = entryLogs.filter(
                            (r) => r.Status == DIRMEMBER_STATUS.CONFIRMED || 
                                   r.Status == DIRMEMBER_STATUS.PENDING || 
                                   r.Status == DIRMEMBER_STATUS.INITIAL
                        );
                        if (confirmedOrUpdateRequestLogs && confirmedOrUpdateRequestLogs.length > 0) {
                            confirmedOrUpdateRequestLogs.sort((a, b) => b.Id - a.Id);
                            const lastConfirmedOrUpdateRequestLog = confirmedOrUpdateRequestLogs[0];
                            director.oldData = lastConfirmedOrUpdateRequestLog;
                        }
                    }
                    
                    director.showHistory = lastLogCreated.Status === DIRMEMBER_STATUS.PENDING;
                    director.canUpdate = lastLogCreated.Status !== DIRMEMBER_STATUS.PENDING;
                    
                    if (lastLogCreated.Status === DIRMEMBER_STATUS.PENDING && lastLogCreated.UpdateRequestDate === null) {
                        const userRequestUpdateLogs = entryLogs.find(r => r.UpdateRequestDate !== null);

                        director.lastChange = {
                            changeType: userRequestUpdateLogs ? userRequestUpdateLogs.TypeOfUpdateRequest : "",
                            changeReason: userRequestUpdateLogs ? userRequestUpdateLogs.UpdateRequestComments : "",
                            status: lastLogCreated.Status
                        };
                    } else {
                        director.lastChange = {
                            changeType: lastLogCreated.TypeOfUpdateRequest,
                            changeReason: lastLogCreated.UpdateRequestComments,
                            status: lastLogCreated.Status
                        };
                    }
                }

                if (director.canUpdate) {
                    const listOfRequiredFields = getListFieldsToValidate(false, director.DirFileType?.toLowerCase(), director.DirOfficerType);

                    // Map profile fields to Director object for validation
                    if (director.directorProfile) {
                        // Common fields
                        director.ServiceAddress = director.directorProfile.MFRSAddress;
                        director.ResidentialOrRegisteredAddress = director.directorProfile.MFRAAddress;
                        
                        // For individual directors
                        if (director.DirFileType?.toLowerCase() === "individual") {
                            director.DateOfBirthOrIncorp = director.directorProfile.MFDateOfBirth;
                            director.PlaceOfBirthOrIncorp = director.directorProfile.MFBirthCountry;
                            director.Nationality = director.directorProfile.MFNationality;
                        } 
                        // For corporate directors - use the actual field names from the profile
                        else {
                            director.MFIncropNr = director.directorProfile.MFIncropNr;
                            director.MFROAddress = director.directorProfile.MFROAddress;
                            director.MFIncorpDate = director.directorProfile.MFIncorpDate;
                            director.MFIncorpCountry = director.directorProfile.MFIncorpCountry;
                        }
                    }

                    const missingValues = listOfRequiredFields.filter((item) => {
                        const fieldValue = director[item.field];
                        const isMissing = fieldValue === null || fieldValue === undefined || fieldValue === "";
                        return isMissing;
                    }).map((item) => item.name);

                    if (missingValues.length > 0) {
                        director.hasMissingValues = true;
                        director.showInfoQuestion = false;
                        director.canConfirm = false;
                        directorsWithMissingValues.push({
                            id: director.UniqueRelationID,
                            name: director.DirName,
                            missingValues: missingValues.join(', ')
                        });
                    }
                }

                // Compare fields between current director and latest history record to check for new data from VP
                let hasVPDataReceived = false;
                if (latestHistory && director) {
                    // Common fields from mem_Directors to compare
                    const directorFieldsToCompare = [
                        'DirName', 'DirCode', 'RelationType', 'DirFileType',
                        'DirStatus', 'DirFromDate', 'DirToDate', 
                        'LicenseeEntityName', 'DirCapacity'
                    ];

                    // Compare main director fields
                    const directorsChanged = directorFieldsToCompare.some(field => {
                        const currentValue = director.dataValues?.[field];
                        const historyValue = latestHistory[field];
                        
                        // Skip if both values are empty/null
                        if ((currentValue === null || currentValue === undefined || currentValue === '') &&
                            (historyValue === null || historyValue === undefined || historyValue === '')) {
                            return false;
                        }
                        
                        // If one value exists and the other doesn't, consider it changed
                        if ((currentValue && !historyValue) || (!currentValue && historyValue)) {
                            return true;
                        }
                        
                        // Special handling for dates
                        if (field === 'DirFromDate' || field === 'DirToDate') {
                            if (!currentValue || !historyValue) return false;
                            
                            // Format dates as YYYY-MM-DD for comparison
                            const currentDate = new Date(currentValue).toISOString().split('T')[0];
                            const historyDate = new Date(historyValue).toISOString().split('T')[0];
                            return currentDate !== historyDate;
                        }
                        
                        // Regular string comparison (case insensitive)
                        if (typeof currentValue === 'string' && typeof historyValue === 'string') {
                            return currentValue.toLowerCase() !== historyValue.toLowerCase();
                        }
                        
                        return currentValue !== historyValue;
                    });

                    // Check profile fields if profile exists
                    let profileChanged = false;
                    
                    if (director.directorProfile?.dataValues) {
                        // Get profile fields to compare based on director type
                        const profileFields = director.DirFileType?.toLowerCase() === 'individual' 
                            ? ['MFFormerName', 'MFDateOfBirth', 'MFBirthCountry', 'MFNationality', 'MFRAAddress', 'MFRSAddress']
                            : ['MFIncropNr', 'MFIncorpCountry', 'MFIncorpDate', 'MFROAddress'];
                        
                        profileChanged = profileFields.some(field => {
                            const currentProfileValue = director.directorProfile.dataValues[field];
                            
                            // Handle dates
                            if (field === 'MFDateOfBirth' || field === 'MFIncorpDate') {
                                if (!currentProfileValue || !latestHistory[`directorProfileHistory.${field}`]) return false;
                                
                                // Format dates as YYYY-MM-DD for comparison
                                const currentDate = new Date(currentProfileValue).toISOString().split('T')[0];
                                const historyDate = new Date(latestHistory[`directorProfileHistory.${field}`]).toISOString().split('T')[0];
                                return currentDate !== historyDate;
                            }

                            // If profile field exists in current data but not in history
                            // or has changed from what was previously there
                            return currentProfileValue && 
                                (currentProfileValue !== latestHistory[`directorProfileHistory.${field}`]);
                        });
                    }

                    hasVPDataReceived = directorsChanged || profileChanged;
                }

                director.confirmedAndUpdated = hasVPDataReceived && entryLogs.some(log => log.Status === DIRMEMBER_STATUS.CONFIRMED);
                return director;
            });
        }

        let individualEntries = directors.filter((e) => e.DirFileType?.toLowerCase() === "individual");
        let corporateEntries = directors.filter((e) => e.DirFileType?.toLowerCase() !== "individual");

        return res.render('director-and-members/directors-forms', {
            masterClientCode: req.params.masterclientcode,
            company: company,
            entityName,
            type: "directors",
            individualEntries: individualEntries,
            corporateEntries: corporateEntries,
            hasCorporateEntries: corporateEntries.length > 0,
            hasDirectors: individualEntries.length > 0 || corporateEntries.length > 0,
            user: req.user,
            messages: req.session.messages,
            directorsWithMissingValues
        });
    } catch (e) {
        console.error("Error getting director entries: ", e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};


exports.getMemberEntries = async function (req, res, next) {
    try {
        if (process.env.HIDE_MEMBERS === 'true') {
            const err = new Error('Members are not available');
            err.status = 404;
            return next(err);
        }

        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if(company?.error){
            const err = new Error(company.error);
            err.status = company.status;
            return next(err);
        }

        let membersWithMissingValues = [];
        let members = await mem_ShareholdersModel.findAll({
            where: {
                EntityLegacyID: company.code,
            },
            include: [{
                model: mem_MemberProfilesModel,
                as: 'shareholderProfile',
                required: false
            }, {
                model: mem_MemberProfilesModel,
                as: 'nominator',
                required: false
            }],
            raw: false
        });

        const listOfRequiredFieldsCorporate = getListFieldsToValidate(true, null, "Corporate");
        const listOfRequiredFieldsIndividual = getListFieldsToValidate(true, null, "Individual");

        let entityName = company.name;
        const individualMembers = [];
        const corporateMembers = [];
        const jointMembers = {};
        const shareholderCodes = [];

        if (members && members.length > 0) {  
            entityName = members[0].EntityName;
            
            // Filter out members with populated MemberDateEnd (Cessation Date)
            members = members.filter(member => !member.MemberDateEnd);
            
            const memberCodes = [...new Set(members.map((e) => e.UniqueRelationID))];

            const historyLogs = await mem_ShareholdersHistoryModel.findAll({
                where: { 
                    EntityLegacyID: company.code, 
                    UniqueRelationID: memberCodes,
                },
                raw: true
            });
            members.forEach((m) => {
                if (shareholderCodes.includes(m.SHUniqueNr) && m.MemberTypeCode !== "JSH" && m.MemberTypeCode !== "JST") {
                    // Skip duplicate entries, not required for shareholder list unless joint or has member
                    if (m.MemberTypeCode === "NOM") {
                        // If shareholder is a nominee, check for missing nominator information
                        const nominator = m.nominator;
                        if (!nominator) {
                            return;
                        }

                        const existingEntry = membersWithMissingValues.find(entry => entry.id === m.SHUniqueNr);
                        if (existingEntry?.missingValues?.includes("Nominator Details") ?? false) {
                            // No need to check again nominator
                            return;
                        }
                        const requiredFieldsNominator = m.MemberFileTypeCode === "I" ? listOfRequiredFieldsIndividual : listOfRequiredFieldsCorporate;
                        const missingNominatorValues = requiredFieldsNominator.filter((item) => {
                            const fieldValue = nominator[item.field];
                            return !fieldValue; // Checks for null, undefined, empty string
                        });

                        if (!missingNominatorValues.length) {
                            return;
                        }

                        if (existingEntry) {
                            existingEntry.missingValues += ', Nominator Details';
                        } else {
                            const isCorporate = ["C", "O", "P", "T", "N", "0"].includes(m.SHFileTypeCode);
                            (isCorporate ? corporateMembers : individualMembers).forEach(entry => {
                                if (entry.SHUniqueNr === m.SHUniqueNr) {
                                    entry.hasMissingValues = true;
                                    entry.showInfoQuestion = false;
                                    entry.canConfirm = false;
                                }
                            });
                            membersWithMissingValues.push({
                                id: m.SHUniqueNr,
                                name: m.SHName,
                                missingValues: "Nominator Details"
                            });
                        }
                    }
                    return;
                }

                const member = { ...m.dataValues };
                member.canUpdate = true;
                member.canConfirm = true;
                member.showHistory = false;
                member.showInfoQuestion = true;
                
                const entryLogs = historyLogs.filter((r) => r.UniqueRelationID === member.UniqueRelationID && r.RelationType === member.RelationType);

                if(entryLogs.length > 0){
                    entryLogs.sort((a, b) => b.Id - a.Id);
                    const lastLogCreated = entryLogs[0];
                    
                    if (lastLogCreated.Status !== DIRMEMBER_STATUS.RECEIVED && lastLogCreated.Status !== DIRMEMBER_STATUS.INITIAL && lastLogCreated.Status !== DIRMEMBER_STATUS.REFRESHED){
                        member.showInfoQuestion = false;
                        member.canConfirm = false;
                    } else if (lastLogCreated.Status == DIRMEMBER_STATUS.RECEIVED || lastLogCreated.Status == DIRMEMBER_STATUS.REFRESHED) {
                        const confirmedOrUpdateRequestLogs = entryLogs.filter((r) => r.Status == DIRMEMBER_STATUS.CONFIRMED || r.Status == DIRMEMBER_STATUS.PENDING || r.Status == DIRMEMBER_STATUS.INITIAL);
                        if (confirmedOrUpdateRequestLogs && confirmedOrUpdateRequestLogs.length > 0) {
                            confirmedOrUpdateRequestLogs.sort((a, b) => b.Id - a.Id);
                            const lastConfirmedOrUpdateRequestLog = confirmedOrUpdateRequestLogs[0];
                            member.oldData = lastConfirmedOrUpdateRequestLog;
                        }
                    }   
                   
                    member.showHistory = lastLogCreated.Status === DIRMEMBER_STATUS.PENDING;
                    member.canUpdate = lastLogCreated.Status !== DIRMEMBER_STATUS.PENDING;
                    
                    if (lastLogCreated.Status === DIRMEMBER_STATUS.PENDING && lastLogCreated.UpdateRequestDate === null){
                        const userRequestUpdateLogs = entryLogs.find(r => r.UpdateRequestDate !== null);

                        member.lastChange = {
                            changeType: userRequestUpdateLogs ? userRequestUpdateLogs.TypeOfUpdateRequest : "",
                            changeReason: userRequestUpdateLogs ? userRequestUpdateLogs.UpdateRequestComments : "",
                            status: lastLogCreated.Status
                        }
                    } else {
                        member.lastChange = {
                            changeType: lastLogCreated.TypeOfUpdateRequest,
                            changeReason: lastLogCreated.UpdateRequestComments,
                            status: lastLogCreated.Status
                        }
                    }
                }

                // Validate member required fields
                if (member.canUpdate) {
                    // Determine member type for validation
                    let requiredFields = listOfRequiredFieldsIndividual;
                    if (["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode)) {
                        requiredFields = listOfRequiredFieldsCorporate;
                    }

                    // Simply check for missing values
                    const missingValues = requiredFields?.filter((item) => {
                        const fieldValue = member[item.field];
                        const profileFieldValue = member[`shareholderProfile.${item.field}`] || member.shareholderProfile?.[item.field];
                        return !fieldValue && !profileFieldValue; // Checks for null, undefined, empty string
                    }).map((item) => item.name)

                    if (member.MemberTypeCode === "NOM") {
                        // If shareholder is a nominee, check for missing nominator information
                        const nominator = member.nominator;
                        if (nominator) {
                            const requiredFieldsNominator = member.MemberFileTypeCode === "I" ? listOfRequiredFieldsIndividual : listOfRequiredFieldsCorporate;
                            const missingNominatorValues = requiredFieldsNominator.filter((item) => {
                                const fieldValue = nominator[item.field];
                                return !fieldValue; // Checks for null, undefined, empty string
                            });
    
                            if (missingNominatorValues.length) {
                                missingValues.push("Nominator Details");
                            }
                        }
                    }

                    if (missingValues.length > 0) {
                        member.hasMissingValues = true;
                        member.showInfoQuestion = false;
                        member.canConfirm = false;
                        membersWithMissingValues.push({
                            id: member.SHUniqueNr,
                            name: member.SHName,
                            missingValues: missingValues.join(', ')
                        });
                    }
                }

                if (member.MemberTypeCode === "JSH" || member.MemberTypeCode === "JST") {
                    if (!jointMembers[member.SHCertNr]) {
                        jointMembers[member.SHCertNr] = [];
                    }
                    jointMembers[member.SHCertNr].push(member);
                } else if (member.SHFileTypeCode === "I") {
                    individualMembers.push(member);
                } else if (["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode)) {
                    corporateMembers.push(member);
                } else {
                    console.log("Unknown member type: ", member.SHFileTypeCode);
                }
                shareholderCodes.push(member.SHUniqueNr);
            });
        }

        const groupedJointMembers = groupKeysByMatchingSHUniqueNr(jointMembers);

        const entity = await mem_EntitiesModel?.findOne({
            where: {
                EntityLegacyID: company.code,
            },
            raw: true
        });
        
        const stockInformationHistory = await mem_EntitiesStockHistory.findAll({
            where: {
                EntityLegacyID: company.code,
            },
            raw: true
        });

        const mutualFundInformationHistory = await mem_EntitiesMutualFundHistory.findAll({
            where: {
                EntityLegacyID: company.code,
            },
            raw: true
        });

        // Get the latest history record for stock and mutual fund information
        const latestStockInformationHistory = stockInformationHistory?.sort((a, b) => b.Id - a.Id)[0];
        const latestMutualFundInformationHistory = mutualFundInformationHistory?.sort((a, b) => b.Id - a.Id)[0];

        let stockInformation = null;
        let mutualFundInformation = null;
        let missingStockFields = [];
        let missingFundFields = [];

        // Validate Stock information
        if (entity?.STXName || entity?.STXTicker || entity?.STXJurisdiction || entity?.STXRegulator) {
            stockInformation = {
                STXName: entity.STXName,
                STXTicker: entity.STXTicker,
                STXJurisdiction: entity.STXJurisdiction,
                STXRegulator: entity.STXRegulator,
                LastUpdateDate: entity.LastUpdateDate,
                STXListingDate: entity.STXListingDate,
                EntityStatus: entity.EntityStatus,
                showHistory: latestStockInformationHistory?.Status === DIRMEMBER_STATUS.PENDING,
                lastChange: {
                    status: latestStockInformationHistory?.Status,
                    changeType: latestStockInformationHistory?.TypeOfUpdateRequest,
                    changeReason: latestStockInformationHistory?.UpdateRequestComments,
                },
                isConfirmed: stockInformationHistory?.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED)
            };

            // Check if record was updated from last history
            if (latestStockInformationHistory) {
                const fields = ['STXName', 'STXTicker', 'STXJurisdiction', 'STXRegulator', 'STXListingDate'];
                const isUpdated = fields.some(field => {
                    if (field === 'STXListingDate') {
                        if (!entity[field] || !latestStockInformationHistory?.[field]) return false;
                        
                        // Format dates as YYYY-MM-DD for comparison
                        const currentDate = new Date(entity[field]).toISOString().split('T')[0];
                        const historyDate = new Date(latestStockInformationHistory?.[field]).toISOString().split('T')[0];
                        return currentDate !== historyDate;
                    }

                    return entity[field] !== latestStockInformationHistory?.[field];
                });
                stockInformation.confirmedAndUpdated = isUpdated && stockInformation.isConfirmed;
                if (stockInformation.confirmedAndUpdated) {
                    stockInformation.showHistory = false;
                }
            }
            
            // Get required fields for Stock entities
            const stockRequiredFields = getListFieldsToValidate(true, null, "Stock");
            
            // Check for missing values
            const missingStock = stockRequiredFields.filter((item) => {
                return !stockInformation[item.field]; // Checks for null, undefined, empty string
            });

            const missingStockNames = missingStock.map((item) => item.name);
            missingStockFields = missingStock.map((item) => item.field);
            
            if (missingStockNames.length > 0) {
                membersWithMissingValues.push({
                    id: company.code,
                    name: "Stock Exchange Information",
                    missingValues: missingStockNames.join(', ')
                });
                
                // Add missing values flag to stock information object
                stockInformation.hasMissingValues = true;
            }
        }
        
        // Validate Mutual Fund information
        if (entity?.BusRegNr || entity?.BusRegType || entity?.BusRegStartDate) {
            mutualFundInformation = {
                Id: company.code,
                BusRegNr: entity.BusRegNr,
                BusRegType: entity.BusRegType,
                BusRegStartDate: entity.BusRegStartDate,
                LastUpdateDate: entity.LastUpdateDate,
                EntityStatus: entity.EntityStatus,
                showHistory: latestMutualFundInformationHistory?.Status === DIRMEMBER_STATUS.PENDING,
                lastChange: {
                    status: latestMutualFundInformationHistory?.Status,
                    changeType: latestMutualFundInformationHistory?.TypeOfUpdateRequest,
                    changeReason: latestMutualFundInformationHistory?.UpdateRequestComments,
                },
                isConfirmed: mutualFundInformationHistory?.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED)
            };

            // Check if record was updated from last history
            if (latestMutualFundInformationHistory) {
                const fields = ['BusRegNr', 'BusRegType', 'BusRegStartDate'];
                const isUpdated = fields.some(field => {
                    if (field === 'BusRegStartDate') {
                        if (!entity[field] || !latestMutualFundInformationHistory?.[field]) return false;
                        
                        // Format dates as YYYY-MM-DD for comparison
                        const currentDate = new Date(entity[field]).toISOString().split('T')[0];
                        const historyDate = new Date(latestMutualFundInformationHistory?.[field]).toISOString().split('T')[0];
                        return currentDate !== historyDate;
                    }

                    return entity[field] !== latestMutualFundInformationHistory?.[field];
                });
                mutualFundInformation.confirmedAndUpdated = isUpdated && mutualFundInformation.isConfirmed;
                if (mutualFundInformation.confirmedAndUpdated) {
                    mutualFundInformation.showHistory = false;
                }
            }

            
            // Get required fields for Mutual Fund entities
            const fundRequiredFields = getListFieldsToValidate(true, null, "Mutual Fund");
            
            // Check for missing values
            const missingFund = fundRequiredFields.filter((item) => {
                return !mutualFundInformation[item.field]; // Checks for null, undefined, empty string
            });

            const missingFundValues = missingFund.map((item) => item.name);
            missingFundFields = missingFund.map((item) => item.field);
            
            if (missingFundValues.length > 0) {
                membersWithMissingValues.push({
                    id: company.code,
                    name: "Mutual Fund Information",
                    missingValues: missingFundValues.join(', ')
                });
                
                // Add missing values flag to mutual fund information object
                mutualFundInformation.hasMissingValues = true;
            }
        }

        const jointMemberList = [];

        for (const certNr in groupedJointMembers) {
            const membersToJoin = groupedJointMembers[certNr];
            const jointMember = {
                SHName: membersToJoin.map(m => m.SHName).join(' & '),
                SHCertNr: certNr,
                ShareIssueDate: membersToJoin[0].ShareIssueDate,
                canUpdate: membersToJoin[0].canUpdate,
                canConfirm: membersToJoin[0].canConfirm,
                showHistory: membersToJoin[0].showHistory,
                hasMissingValues: membersToJoin.some(m => m.hasMissingValues),
                lastChange: membersToJoin[0].lastChange
            };
            jointMemberList.push(jointMember);
        }

        return res.render('director-and-members/member-forms', {
            masterClientCode: req.params.masterclientcode,
            company: company,
            entityName,
            type: "members",
            user: req.user,
            messages: req.session.messages,
            individualMembers,
            jointMembers: jointMemberList,
            corporateMembers,
            membersWithMissingValues,
            stockInformation,
            mutualFundInformation,
            missingStockFields,
            missingFundFields,
            hasInformation: stockInformation ||
                mutualFundInformation ||
                individualMembers.length > 0 ||
                corporateMembers.length > 0 ||
                jointMemberList.length > 0,
            hasShareholders: members && members.length > 0,
        });
    } catch (e) {
        console.error("Error getting member entries: ", e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};


exports.getDirectorDetails = async function (req, res, next) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if(company?.error){
            const err = new Error(company.error);
            err.status = company.status;
            return next(err);
        }

        const type = TYPE_OF_DIRECTOR_DIR;
        
        // Get the director by ID from mem_Directors table with associated profile
        let director = await mem_DirectorsModel.findOne({
            where: {
                EntityLegacyID: company.code,
                UniqueRelationID: req.params.id,
            },
            include: [{
                model: mem_MemberProfilesModel,
                as: 'directorProfile',
                required: false
            }],
            raw: false
        });

        if (!director) {
            const err = new Error('Director not found');
            err.status = 404;
            return next(err);
        }

        // Get director history records
        let directorHistoryRecords = await mem_DirectorsHistoryModel.findAll({
            where: {
                EntityLegacyID: company.code,
                UniqueRelationID: director.UniqueRelationID
            },
            include: [{
                model: mem_MemberProfilesHistoryModel,
                as: 'directorProfileHistory',
                required: false
            }],
            raw: true,
            order: [['Id', 'DESC']],
        });

        // Check if director has confirmed
        const isConfirmedNew = directorHistoryRecords.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED);
        
        // Get the latest history record if any exist
        const latestHistory = directorHistoryRecords.length > 0 ? directorHistoryRecords[0] : null;
        
        // Compare fields between current director and latest history record to check for new data from VP
        let hasVPDataReceived = false;
        if (latestHistory && director) {
            // Common fields from mem_Directors to compare
            const directorFieldsToCompare = [
                'DirName', 'DirCode', 'RelationType', 'DirFileType',
                'DirStatus', 'DirFromDate', 'DirToDate', 
                'LicenseeEntityName', 'DirCapacity'
            ];

            // Compare main director fields
            const directorsChanged = directorFieldsToCompare.some(field => {
                const currentValue = director.dataValues?.[field];
                const historyValue = latestHistory[field];
                
                // Skip if both values are empty/null
                if ((currentValue === null || currentValue === undefined || currentValue === '') &&
                    (historyValue === null || historyValue === undefined || historyValue === '')) {
                    return false;
                }
                
                // If one value exists and the other doesn't, consider it changed
                if ((currentValue && !historyValue) || (!currentValue && historyValue)) {
                    return true;
                }
                
                // Special handling for dates
                if (field === 'DirFromDate' || field === 'DirToDate') {
                    if (!currentValue || !historyValue) return false;
                    
                    // Format dates as YYYY-MM-DD for comparison
                    const currentDate = new Date(currentValue).toISOString().split('T')[0];
                    const historyDate = new Date(historyValue).toISOString().split('T')[0];
                    return currentDate !== historyDate;
                }
                
                // Regular string comparison (case insensitive)
                if (typeof currentValue === 'string' && typeof historyValue === 'string') {
                    return currentValue.toLowerCase() !== historyValue.toLowerCase();
                }
                
                return currentValue !== historyValue;
            });

            // Check profile fields if profile exists
            let profileChanged = false;
            
            if (director.directorProfile?.dataValues) {
                // Get profile fields to compare based on director type
                const profileFields = director.DirFileType?.toLowerCase() === 'individual' 
                    ? ['MFFormerName', 'MFDateOfBirth', 'MFBirthCountry', 'MFNationality', 'MFRAAddress', 'MFRSAddress']
                    : ['MFIncropNr', 'MFIncorpCountry', 'MFIncorpDate', 'MFROAddress'];
                
                profileChanged = profileFields.some(field => {
                    const currentProfileValue = director.directorProfile.dataValues[field];
                    
                    // Handle dates
                    if (field === 'MFDateOfBirth' || field === 'MFIncorpDate') {
                        if (!currentProfileValue || !latestHistory[`directorProfileHistory.${field}`]) return false;
                        
                        // Format dates as YYYY-MM-DD for comparison
                        const currentDate = new Date(currentProfileValue).toISOString().split('T')[0];
                        const historyDate = new Date(latestHistory[`directorProfileHistory.${field}`]).toISOString().split('T')[0];
                        return currentDate !== historyDate;
                    }

                    // If profile field exists in current data but not in history
                    // or has changed from what was previously there
                    return currentProfileValue && 
                        (currentProfileValue !== latestHistory[`directorProfileHistory.${field}`]);
                });
            }
            
            hasVPDataReceived = directorsChanged || profileChanged;
        }

        // Get the history logs for this director from old VpDirectorInfoHistory table
        let historyLogs = [];
        try {
            historyLogs = await VpDirectorInfoHistoryModel.findAll({
                where: { 
                    CompanyNumber: company.code,
                    RelationType: type,
                    UniqueRelationId: director.PreviousUniqueRelationId
                },
                raw: true
            });
        } catch (error) {
            historyLogs = [];
        }

        if (historyLogs && historyLogs.length > 0) {
            historyLogs.sort((a, b) => b.Id - a.Id);
        }

        // Check if confirmed in old VP history logs - this drives the blue highlighting
        // Only apply if there are no new history records
        const hasNewHistoryRecords = directorHistoryRecords?.length > 0
        const isConfirmedInHistory = !hasNewHistoryRecords && historyLogs && historyLogs.length > 0 && 
            historyLogs.some(log => log.Status === DIRMEMBER_STATUS.CONFIRMED);
        
        // Create a properly formatted directorForView with all necessary fields
        let directorForView;
        let isConfirmed = latestHistory?.Status === DIRMEMBER_STATUS.CONFIRMED;
        
        // Check the actual value of isLicensed in the database
        const isLicensed = latestHistory?.isLicensed;


        if (director) {
            // Base fields for both types
            directorForView = {
                // Core fields required for functionality
                UniqueRelationID: director.UniqueRelationID,
                
                // Fields displayed in both views
                Name: director.DirName,
                Code: director.DirCode,
                OfficerType: director.DirOfficerType,
                DirectorCapacity: director.DirCapacity || '',
                LicenseeEntity: director.LicenseeEntityName || '',
                LicenseeEntityName: director.LicenseeEntityName || '',
                isLicensed: isLicensed,
            };

            // FileType determines if individual or corporate
            if (director.DirFileType?.toLowerCase() === 'individual') {
                // Fields specific to Individual directors
                directorForView.FileType = director.DirFileType;
                directorForView.FromDate = director.DirFromDate;
                directorForView.ToDate = director.DirToDate;
                directorForView.FormerName = director.directorProfile?.MFFormerName || '';
                directorForView.ServiceAddress = director.directorProfile?.MFRSAddress || '';
                directorForView.ResidentialOrRegisteredAddress = director.directorProfile?.MFRAAddress || '';
                directorForView.DateOfBirthOrIncorp = director.directorProfile?.MFDateOfBirth || '';
                directorForView.PlaceOfBirthOrIncorp = director.directorProfile?.MFBirthCountry || '';
                directorForView.Nationality = director.directorProfile?.MFNationality || '';
            } else {
                // Fields specific to Corporate directors
                directorForView.FileType = director.DirFileType;
                directorForView.FromDate = director.DirFromDate;
                directorForView.ToDate = director.DirToDate;
                directorForView.BoDirIncorporationNumber = director.directorProfile?.MFIncropNr || '';
                directorForView.PlaceOfBirthOrIncorp = director.directorProfile?.MFIncorpCountry || '';
                directorForView.DateOfBirthOrIncorp = director.directorProfile?.MFIncorpDate || '';
                directorForView.ResidentialOrRegisteredAddress = director.directorProfile?.MFROAddress || '';
            }
        }

        const listOfRequiredFields = getListFieldsToValidate(
            type === TYPE_OF_MEMBER, 
            director.DirFileType?.toLowerCase(), 
            director.DirOfficerType
        );

        if (director.directorProfile) {
            // Common fields
            director.ServiceAddress = director.directorProfile.MFRSAddress;
            director.ResidentialOrRegisteredAddress = director.directorProfile.MFRAAddress;
            
            // For individual directors
            if (director.DirFileType?.toLowerCase() === "individual") {
                director.DateOfBirthOrIncorp = director.directorProfile.MFDateOfBirth;
                director.PlaceOfBirthOrIncorp = director.directorProfile.MFBirthCountry;
                director.Nationality = director.directorProfile.MFNationality;
            } 
            // For corporate directors - use the actual field names from the profile
            else {
                director.MFIncropNr = director.directorProfile.MFIncropNr;
                director.MFROAddress = director.directorProfile.MFROAddress;
                director.MFIncorpDate = director.directorProfile.MFIncorpDate;
                director.MFIncorpCountry = director.directorProfile.MFIncorpCountry;
            }
        }
        
        const missingValues = listOfRequiredFields.filter((item) => {
            const fieldValue = director[item.field];
            const isMissing = fieldValue === null || fieldValue === undefined || fieldValue === "";
            return isMissing;
        }).map((item) => item.field);

        return res.render('director-and-members/director-details-forms', {
            masterClientCode: req.params.masterclientcode,
            company: company,
            director: directorForView,
            isIndividual: directorForView.FileType?.toLowerCase() === 'individual',
            isCorporate: directorForView.FileType?.toLowerCase() !== 'individual',
            hasDirectorCapacity: !!directorForView.DirectorCapacity,
            hasLicenseeEntity: !!directorForView.LicenseeEntityName || !!directorForView.LicenseeEntity,
            showConfirmButton: !isConfirmed && missingValues.length === 0,
            missingValues,
            isConfirmedInHistory: isConfirmedInHistory, // This is what controls the blue highlighting
            isConfirmedNew,
            user: req.user,
            messages: req.session.messages,
            isDirectorLicensed: isLicensed,
            hasVPDataReceived: hasVPDataReceived
        });
    } catch (e) {
        console.error("Error getting director details: ", e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};


exports.getMemberDetails = async function (req, res, next) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if(company?.error){
            const err = new Error(company.error);
            err.status = company.status;
            return next(err);
        }
        // Get the members by ID from mem_Shareholders table with associated profile
        let members = await mem_ShareholdersModel.findAll({
            where: {
                EntityLegacyID: company.code,
                SHUniqueNr: req.params.id,
            },
            include: [{
                model: mem_MemberProfilesModel,
                as: 'shareholderProfile',
                required: false
            }, {
                model: mem_ShareholdersHistoryModel,
                as: 'historyRecords',
                required: false
            }],
            raw: false
        });

        const isNominee = members.some(m => m.MemberTypeCode === 'NOM');

        const memberCodes = [...new Set(members.map((e) => e.MemberCode))];
        const nominators = await mem_MemberProfilesModel.findAll({
            where: {
                MFCode: memberCodes,
            },
            raw: true
        });

        const listOfRequiredFieldsCorporate = getListFieldsToValidate(true, null, "Corporate");
        const listOfRequiredFieldsIndividual = getListFieldsToValidate(true, null, "Individual");

        // Match member attributes to the correct profile
        const individualNominators = [];
        const corporateNominators = [];
        const missingValuesNominators = [];
        nominators.forEach((n) => {
            const member = members.find((m) => m.MemberCode === n.MFCode);
            const details = {
                MemberName: member.MemberName,
                MemberDateStart: member.MemberDateStart,
                ...n
            };
            if (n.MFCode === member.MemberCode) {
                if (member.MemberFileTypeCode === "I") {
                    // Validate required fields for individual nominators
                    const missingValuesNominator = listOfRequiredFieldsIndividual.filter((item) => {
                        const fieldValue = details[item.field];
                        return !fieldValue; // Checks for null, undefined, empty string
                    }).map((item) => item.field);
                    missingValuesNominators.push(...missingValuesNominator);

                    individualNominators.push(details);
                } else {
                    // Validate required fields for corporate nominators
                    const missingValuesNominator = listOfRequiredFieldsCorporate.filter((item) => {
                        const fieldValue = details[item.field];
                        return !fieldValue; // Checks for null, undefined, empty string
                    }).map((item) => item.field);
                    missingValuesNominators.push(...missingValuesNominator);

                    corporateNominators.push(details);
                }
            }
        });

        let shareDetails = [];
        members.forEach((m) => {
            if (m.SHCertNr && m.NrOfShares && !shareDetails.find(d => d.SHCertNr === m.SHCertNr)) {
                shareDetails.push({
                    SHCertNr: m.SHCertNr,
                    NrOfShares: m.NrOfShares,
                    ShareClassName: m.ShareClassName,
                    SHVotingRights: m.SHVotingRights,
                    ShareIssueDate: m.ShareIssueDate
                });
            }
        })

        // Sort shares by issue date (oldest to newest)
        shareDetails.sort((a, b) => new Date(a.ShareIssueDate) - new Date(b.ShareIssueDate));
        
        // All records should share the same history so just use the first one
        const entryLogs = members[0].historyRecords;
        
        const member = members[0];
        // Date entered should be first share issue date
        member.dateEntered = shareDetails.length > 0 ? Math.min(...shareDetails.map(d => new Date(d.ShareIssueDate))) : null;

        let lastLogCreated;
        if (entryLogs.length > 0) {
            entryLogs.sort((a, b) => b.Id - a.Id);
            lastLogCreated = entryLogs[0];
            
            if (lastLogCreated.Status !== DIRMEMBER_STATUS.RECEIVED && 
                lastLogCreated.Status !== DIRMEMBER_STATUS.INITIAL && 
                lastLogCreated.Status !== DIRMEMBER_STATUS.REFRESHED
            ) {
                member.canConfirm = false;
            }
            
            member.showHistory = lastLogCreated.Status === DIRMEMBER_STATUS.PENDING;
            member.canUpdate = lastLogCreated.Status !== DIRMEMBER_STATUS.PENDING;
            
            if (lastLogCreated.Status === DIRMEMBER_STATUS.PENDING && lastLogCreated.UpdateRequestDate === null) {
                const userRequestUpdateLogs = entryLogs.find(r => r.UpdateRequestDate !== null);

                member.lastChange = {
                    changeType: userRequestUpdateLogs ? userRequestUpdateLogs.TypeOfUpdateRequest : "",
                    changeReason: userRequestUpdateLogs ? userRequestUpdateLogs.UpdateRequestComments : "",
                    status: lastLogCreated.Status
                };
            } else {
                member.lastChange = {
                    changeType: lastLogCreated.TypeOfUpdateRequest,
                    changeReason: lastLogCreated.UpdateRequestComments,
                    status: lastLogCreated.Status
                };
            }
        }

        if (!member) {
            const err = new Error('Member not found');
            err.status = 404;
            return next(err);
        }

        // Determine member type for validation
        let memberType;
        if (member.MemberTypeCode === "JSH" || member.MemberTypeCode === "JST") {
            throw new Error('Joint members are not supported in this view');
        } else if (member.SHFileTypeCode === "I") {
            memberType = "Individual";
        } else if (["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode)) {
            memberType = "Corporate";
        }

        // Get required fields based on member type
        const listOfRequiredFields = memberType === "Individual" ? listOfRequiredFieldsIndividual : listOfRequiredFieldsCorporate;
        
        // Simply check for missing values without extra mapping
        const missingValues = listOfRequiredFields?.filter((item) => {
            const fieldValue = member[item.field];
            const profileFieldValue = member.shareholderProfile?.[item.field];
            return !fieldValue && !profileFieldValue; // Checks for null, undefined, empty string
        }).map((item) => item.field);

        // Check if member has confirmed status in history records
        let isConfirmed = false;
        if (member.historyRecords && member.historyRecords.length > 0) {
            isConfirmed = member.historyRecords.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED);
        }

        return res.render('director-and-members/member-details-forms', {
            masterClientCode: req.params.masterclientcode,
            company: company,
            user: req.user,
            messages: req.session.messages,
            memberDetails: member,
            isIndividual: member.SHFileTypeCode === "I",
            isCorporate: ["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode),
            nominators,
            hasNominators: nominators.length > 0 && isNominee,
            isNominee,
            missingValuesNominators,
            individualNominators,
            corporateNominators,
            showShareDetails: shareDetails.length > 0,
            shareDetails,
            showConfirmButton: !isConfirmed && !missingValues.length && !missingValuesNominators.length,
            missingValues: missingValues,
            missingAnyValue: missingValues.length > 0 || missingValuesNominators.length > 0,
            isConfirmed: isConfirmed,
            hasNomineeArrangement: lastLogCreated?.hasNomineeArrangement
        });
    } catch (e) {
        console.error("Error getting member details: ", e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};

exports.getJointMemberDetails = async function (req, res, next) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if(company?.error){
            const err = new Error(company.error);
            err.status = company.status;
            return next(err);
        }
        // Get the members by code and CertNr from mem_Shareholders table with associated profile
        let members = await mem_ShareholdersModel.findAll({
            where: {
                EntityLegacyID: company.code,
                // Pick first cert number from grouped list, all should share the same history but Share details
                SHCertNr: req.params.certNr.split("-")[0]
            },
            include: [{
                model: mem_MemberProfilesModel,
                as: 'shareholderProfile',
                required: false
            }, {
                model: mem_ShareholdersHistoryModel,
                as: 'historyRecords',
                required: false
            }],
            raw: false
        });

        if (!members || members.length === 0) {
            const err = new Error('Members not found');
            err.status = 404;
            return next(err);
        }
        
        const listOfRequiredFieldsIndividual = getListFieldsToValidate(true, null, "Individual");
        const listOfRequiredFieldsCorporate = getListFieldsToValidate(true, null, "Corporate");
        const corporateMembers = [];
        const individualMembers = [];
        const missingValues = [];
        let hasNomineeArrangement = null;
        members.forEach((member) => {
            let lastLogCreated;
            if (member.historyRecords.length > 0) {
                member.historyRecords.sort((a, b) => b.Id - a.Id);
                lastLogCreated = member.historyRecords[0];
                hasNomineeArrangement = lastLogCreated.hasNomineeArrangement;
                
                if (lastLogCreated.Status !== DIRMEMBER_STATUS.RECEIVED && 
                    lastLogCreated.Status !== DIRMEMBER_STATUS.INITIAL && 
                    lastLogCreated.Status !== DIRMEMBER_STATUS.REFRESHED
                ) {
                    member.canConfirm = false;
                }
                
                member.showHistory = lastLogCreated.Status === DIRMEMBER_STATUS.PENDING;
                member.canUpdate = lastLogCreated.Status !== DIRMEMBER_STATUS.PENDING;
                
                if (lastLogCreated.Status === DIRMEMBER_STATUS.PENDING && lastLogCreated.UpdateRequestDate === null) {
                    const userRequestUpdateLogs = member.historyRecords.find(r => r.UpdateRequestDate !== null);

                    member.lastChange = {
                        changeType: userRequestUpdateLogs ? userRequestUpdateLogs.TypeOfUpdateRequest : "",
                        changeReason: userRequestUpdateLogs ? userRequestUpdateLogs.UpdateRequestComments : "",
                        status: lastLogCreated.Status
                    };
                } else {
                    member.lastChange = {
                        changeType: lastLogCreated.TypeOfUpdateRequest,
                        changeReason: lastLogCreated.UpdateRequestComments,
                        status: lastLogCreated.Status
                    };
                }
            }
            let isCorporate = false;
            if (["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode)) {
                isCorporate = true;
            }

            // Simply check for missing values without extra mapping
            member.missingValues = (isCorporate ? listOfRequiredFieldsCorporate : listOfRequiredFieldsIndividual).filter((item) => {
                const fieldValue = member[item.field];
                const profileFieldValue = member.shareholderProfile?.[item.field];
                return !fieldValue && !profileFieldValue; // Checks for null, undefined, empty string
            }).map((item) => item.field);

            missingValues.push(...member.missingValues);

            if (member.historyRecords && member.historyRecords.length > 0) {
                member.isConfirmed = member.historyRecords.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED);
            }

            if (isCorporate) {
                corporateMembers.push(member);
            } else {
                individualMembers.push(member);
            }
            
        })

        let shareDetails = await mem_ShareholdersModel.findAll({
            where: {
                EntityLegacyID: company.code,
                SHCertNr: req.params.certNr.split("-")
            },
            raw: false
        });

        // Only one share detail per cert number
        shareDetails = shareDetails.filter((share, index, self) =>
            index === self.findIndex((t) => (
                t.SHCertNr === share.SHCertNr
            ))
        );

        // Sort shares by issue date (oldest to newest)
        shareDetails.sort((a, b) => new Date(a.ShareIssueDate) - new Date(b.ShareIssueDate));

        const isConfirmed = individualMembers.some(member => member.isConfirmed) || corporateMembers.some(member => member.isConfirmed);

        console.log("hasNomineeArrangement: ", hasNomineeArrangement);

        return res.render('director-and-members/joint-member-details-forms', {
            masterClientCode: req.params.masterclientcode,
            company: company,
            user: req.user,
            messages: req.session.messages,
            corporateMembers,
            individualMembers,
            shareDetails,
            shareNumbers: req.params.certNr,
            hasCorporateMembers: corporateMembers.length > 0,
            hasIndividualMembers: individualMembers.length > 0,
            allMembers: [...corporateMembers, ...individualMembers],
            showConfirmButton: !isConfirmed && !missingValues.length,
            missingValues: missingValues,
            isConfirmed: isConfirmed,
            hasNomineeArrangement: hasNomineeArrangement
        });
    } catch (e) {
        console.error("Error getting member details: ", e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};

function getListFieldsToValidate(isMember, filetype, officerType){
    const fieldsByType = isMember ? MEMBER_REQUIRED_FIELDS : DIRECTOR_REQUIRED_FIELDS;
    let fieldsToValidate = [];

    if (isMember){
        fieldsToValidate = fieldsByType[officerType] || [];
            
    }else{
        // Check if the filetype is individual 
        const isIndividual = filetype && filetype.toLowerCase() === "individual";
        fieldsToValidate = isIndividual ? fieldsByType["individual"] : fieldsByType["corporate"];
    }
    
    return fieldsToValidate
}


exports.createDirectorConfirmationLog = async function (req, res) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
        const data = req.body;

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }
        
        // Only allow directors and members as type
        if (req.originalUrl.includes('/director-and-members/')) {
            if (!req.originalUrl.includes('/directors/') && !req.originalUrl.includes('/members/')) {
                return res.status(404).json({
                    "status": 404,
                    "error": 'Director information not found'
                });
            }
        }
        // Get director info from mem_Directors table
        let director = await mem_DirectorsModel.findOne({
            where: {
                EntityLegacyID: company.code,
                UniqueRelationID: req.params.id,
            },
            include: [{
                model: mem_MemberProfilesModel,
                as: 'directorProfile',
                required: false
            }],
            raw: false
        });

        if (!director) {
            return res.status(404).json({
                "status": 404,
                "error": 'Director information not found'
            });
        }

        // Set licensee entity code based on user input (radio button)
        const isDirectorLicensed = data.isDirectorLicensed === 'yes';
        
        // Create log in mem_DirectorsHistory table
        let confirmDirectorDataLog;
        try {
            // Create a deep copy of director data to avoid reference issues
            const directorJSON = director.toJSON();
            
            confirmDirectorDataLog = await mem_DirectorsHistoryModel.create({
                ...directorJSON,
                ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                Status: DIRMEMBER_STATUS.CONFIRMED,
                UserEmail: req.user.username, 
                TypeOfUpdateRequest: null,
                UpdateRequestComments: null,
                isLicensed: isDirectorLicensed
            });
            confirmDirectorDataLog = confirmDirectorDataLog.toJSON();
            
            // Create corresponding record in mem_MemberProfilesHistory if a profile exists
            if (director.directorProfile && confirmDirectorDataLog.Id) {
                try {
                    const profileData = {
                        DirectorHistoryId: confirmDirectorDataLog.Id,
                        MFCode: director.directorProfile.MFCode,
                        MFUniqueNr: director.directorProfile.MFUniqueNr,
                        MFName: director.directorProfile.MFName,
                        MFTypeCode: director.directorProfile.MFTypeCode,
                        MFLegacyID: director.directorProfile.MFLegacyID,
                        MFType: director.directorProfile.MFType,
                        MFIncropNr: director.directorProfile.MFIncropNr,
                        MFIncorpDate: director.directorProfile.MFIncorpDate,
                        MFIncorpCountry: director.directorProfile.MFIncorpCountry,
                        MFDateOfBirth: director.directorProfile.MFDateOfBirth,
                        MFFormerName: director.directorProfile.MFFormerName,
                        MFBirthCountry: director.directorProfile.MFBirthCountry,
                        MFNationality: director.directorProfile.MFNationality,
                        MFRAAddress: director.directorProfile.MFRAAddress,
                        MFROAddress: director.directorProfile.MFROAddress,
                        MFRSAddress: director.directorProfile.MFRSAddress,
                        MFStatusCode: director.directorProfile.MFStatusCode,
                        MFStatus: director.directorProfile.MFStatus,
                        MFProductionOffice: director.directorProfile.MFProductionOffice
                    };
                    
                    await mem_MemberProfilesHistoryModel.create(profileData);
                } catch (error) {
                    console.error("Error creating MemberProfilesHistory record:", error);
                    // Continue execution even if this fails
                }
            }
            
        } catch (error) {
            console.error("Error creating record in mem_DirectorsHistory:", error);
        }
        
        // Determine message based on the URL path and type
        let successMessage;
        if (req.originalUrl.includes('/director-and-bo/')) {
            successMessage = `${req.params.type === "directors" ? "Director" : "BO"} information has been confirmed successfully`;
        } else if (req.originalUrl.includes('/directors/')) {
            successMessage = "We have received your confirmation to director information and will process the data to BVI Registry in due course if we haven't submit before.";
        } else if (req.originalUrl.includes('/members/')) {
            successMessage = "Member information has been confirmed successfully";
        } else {
            successMessage = `${req.params.type === "directors" ? "Director" : "Member"} information has been confirmed successfully`;
        }

        // Return success if confirmation log was created successfully
        if (confirmDirectorDataLog?.Id) {
            return res.status(200).json({ "status": 200, "message": successMessage });
        } else {
            return res.status(500).json({ "status": 500, "message": "There was an error in the process to confirm the information" });
        }

    } catch (e) {
        console.error("Error in process to confirm director data: ", e);
        return res.status(500).json({
            "status": 500,
            "error": 'Internal server error'
        });
    }
}


exports.createMemberConfirmationLog = async function (req, res) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }
        
        let memberProfile = await mem_MemberProfilesModel.findOne({
            where: {
                MFUniqueNr: req.params.id,
            },
            raw: false
        });

        if (!memberProfile) {
            return res.status(404).json({
                "status": 404,
                "error": 'Member information not found'
            });
        }

        // Get ALL share records for this shareholder
        let allShareholderRows = await mem_ShareholdersModel.findAll({
            where: {
                EntityLegacyID: company.code,
                SHUniqueNr: req.params.id,
            },
            raw: false
        });

        if (!allShareholderRows || allShareholderRows.length === 0) {
            return res.status(404).json({
                "status": 404,
                "error": 'No shareholder records found'
            });
        }

        // Get all nominator profiles (linked through MemberCode)
        const nominatorProfilesByMemberCode = {};
        for (const share of allShareholderRows) {
            if (share.MemberCode) {
                if (!nominatorProfilesByMemberCode[share.MemberCode]) {
                    const nominatorProfile = await mem_MemberProfilesModel.findOne({
                        where: {
                            MFCode: share.MemberCode
                        },
                        raw: false
                    });
                    
                    if (nominatorProfile) {
                        nominatorProfilesByMemberCode[share.MemberCode] = nominatorProfile;
                    }
                }
            }
        }
       
        for (const share of allShareholderRows) {
            // Create history record for this share
            const historyRecord = await mem_ShareholdersHistoryModel.create({
                UniqueRelationID: share.UniqueRelationID,
                ClientCode: share.ClientCode,
                ClientName: share.ClientName,
                ClientUniqueNr: share.ClientUniqueNr,
                EntityCode: share.EntityCode,
                EntityName: share.EntityName,
                EntityUniqueNr: share.EntityUniqueNr,
                EntityLegacyID: share.EntityLegacyID,
                RelationType: share.RelationType,
                SHCode: share.SHCode,
                SHName: share.SHName,
                SHUniqueNr: share.SHUniqueNr,
                SHFileTypeCode: share.SHFileTypeCode,
                SHFileType: share.SHFileType,
                MemberTypeCode: share.MemberTypeCode,
                MemberType: share.MemberType,
                MemberCode: share.MemberCode,
                MemberUniqueNr: share.MemberUniqueNr,
                MemberName: share.MemberName,
                MemberFileTypeCode: share.MemberFileTypeCode,
                MemberFileType: share.MemberFileType,
                MemberDateStart: share.MemberDateStart,
                MemberDateEnd: share.MemberDateEnd,
                ShareTypeCode: share.ShareTypeCode,
                ShareClassName: share.ShareClassName,
                SHVotingRights: share.SHVotingRights,
                ShareIssueDate: share.ShareIssueDate,
                SHCertNr: share.SHCertNr,
                NrOfShares: share.NrOfShares,
                SHAddress: share.SHAddress || '',
                BenOwnerCode: share.BenOwnerCode,
                BenOwner: share.BenOwner,
                BenOwnerCertNr: share.BenOwnerCertNr,
                ShareholderID: share.ShareholderID,
                ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                Status: DIRMEMBER_STATUS.CONFIRMED,
                UserEmail: req.user.username,
                hasNomineeArrangement: req.body.hasNomineeArrangement
            });
            
            await mem_MemberProfilesHistoryModel.create({
                ShareholderHistoryId: historyRecord.Id,
                MFCode: memberProfile.MFCode,
                MFUniqueNr: memberProfile.MFUniqueNr,
                MFName: memberProfile.MFName,
                MFTypeCode: memberProfile.MFTypeCode,
                MFLegacyID: memberProfile.MFLegacyID,
                MFType: memberProfile.MFType,
                MFIncropNr: memberProfile.MFIncropNr,
                MFIncorpDate: memberProfile.MFIncorpDate,
                MFIncorpCountry: memberProfile.MFIncorpCountry,
                MFDateOfBirth: memberProfile.MFDateOfBirth,
                MFFormerName: memberProfile.MFFormerName,
                MFBirthCountry: memberProfile.MFBirthCountry,
                MFNationality: memberProfile.MFNationality,
                MFRAAddress: memberProfile.MFRAAddress,
                MFROAddress: memberProfile.MFROAddress,
                MFRSAddress: memberProfile.MFRSAddress,
                MFStatusCode: memberProfile.MFStatusCode,
                MFStatus: memberProfile.MFStatus,
                MFProductionOffice: memberProfile.MFProductionOffice
            });

            // If this share has a nominator, create history for that nominator's profile
            if (share.MemberCode && nominatorProfilesByMemberCode[share.MemberCode]) {
                const nominatorProfile = nominatorProfilesByMemberCode[share.MemberCode];
                
                await mem_MemberProfilesHistoryModel.create({
                    ShareholderHistoryId: historyRecord.Id,
                    MFCode: nominatorProfile.MFCode,
                    MFUniqueNr: nominatorProfile.MFUniqueNr,
                    MFName: nominatorProfile.MFName,
                    MFTypeCode: nominatorProfile.MFTypeCode,
                    MFLegacyID: nominatorProfile.MFLegacyID,
                    MFType: nominatorProfile.MFType,
                    MFIncropNr: nominatorProfile.MFIncropNr,
                    MFIncorpDate: nominatorProfile.MFIncorpDate,
                    MFIncorpCountry: nominatorProfile.MFIncorpCountry,
                    MFDateOfBirth: nominatorProfile.MFDateOfBirth,
                    MFFormerName: nominatorProfile.MFFormerName,
                    MFBirthCountry: nominatorProfile.MFBirthCountry,
                    MFNationality: nominatorProfile.MFNationality,
                    MFRAAddress: nominatorProfile.MFRAAddress,
                    MFROAddress: nominatorProfile.MFROAddress,
                    MFRSAddress: nominatorProfile.MFRSAddress,
                    MFStatusCode: nominatorProfile.MFStatusCode,
                    MFStatus: nominatorProfile.MFStatus,
                    MFProductionOffice: nominatorProfile.MFProductionOffice
                });
            }
        }



        return res.status(200).json({
            "status": 200,
            "message": "We have received your confirmation to member information and will process the data to BVI Registry in due course if we haven't submit before."
        });

    } catch (error) {
        console.error('Error in process to confirm member data: ', error);
        return res.status(500).json({
            "status": 500,
            "error": 'Failed to confirm member information'
        });
    }
}

exports.createJointMemberConfirmationLog = async function (req, res) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }
        
        // Get all member records from the database with the given UniqueRelationID
        let shareholderRows = await mem_ShareholdersModel.findAll({
            where: {
                EntityLegacyID: company.code,
                SHCertNr: req.params.certNr.split("-")
            },
            include: [{
                model: mem_MemberProfilesModel,
                as: 'shareholderProfile',
                required: false
            }],
            raw: false
        });

        if (!shareholderRows || shareholderRows.length === 0) {
            return res.status(404).json({
                "status": 404,
                "error": 'Member information not found'
            });
        }


        
        for (const share of shareholderRows) {
            // Create history record for this share
            const historyRecord = await mem_ShareholdersHistoryModel.create({
                UniqueRelationID: share.UniqueRelationID,
                ClientCode: share.ClientCode,
                ClientName: share.ClientName,
                ClientUniqueNr: share.ClientUniqueNr,
                EntityCode: share.EntityCode,
                EntityName: share.EntityName,
                EntityUniqueNr: share.EntityUniqueNr,
                EntityLegacyID: share.EntityLegacyID,
                RelationType: share.RelationType,
                SHCode: share.SHCode,
                SHName: share.SHName,
                SHUniqueNr: share.SHUniqueNr,
                SHFileTypeCode: share.SHFileTypeCode,
                SHFileType: share.SHFileType,
                MemberTypeCode: share.MemberTypeCode,
                MemberType: share.MemberType,
                MemberCode: share.MemberCode,
                MemberUniqueNr: share.MemberUniqueNr,
                MemberName: share.MemberName,
                MemberFileTypeCode: share.MemberFileTypeCode,
                MemberFileType: share.MemberFileType,
                MemberDateStart: share.MemberDateStart,
                MemberDateEnd: share.MemberDateEnd,
                ShareTypeCode: share.ShareTypeCode,
                ShareClassName: share.ShareClassName,
                SHVotingRights: share.SHVotingRights,
                ShareIssueDate: share.ShareIssueDate,
                SHCertNr: share.SHCertNr,
                NrOfShares: share.NrOfShares,
                SHAddress: share.SHAddress || '',
                BenOwnerCode: share.BenOwnerCode,
                BenOwner: share.BenOwner,
                BenOwnerCertNr: share.BenOwnerCertNr,
                ShareholderID: share.ShareholderID,
                ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                Status: DIRMEMBER_STATUS.CONFIRMED,
                UserEmail: req.user.username,
                hasNomineeArrangement: req.body.hasNomineeArrangement
            });
            
            await mem_MemberProfilesHistoryModel.create({
                ShareholderHistoryId: historyRecord.Id,
                MFCode: share.shareholderProfile.MFCode,
                MFUniqueNr: share.shareholderProfile.MFUniqueNr,
                MFName: share.shareholderProfile.MFName,
                MFTypeCode: share.shareholderProfile.MFTypeCode,
                MFLegacyID: share.shareholderProfile.MFLegacyID,
                MFType: share.shareholderProfile.MFType,
                MFIncropNr: share.shareholderProfile.MFIncropNr,
                MFIncorpDate: share.shareholderProfile.MFIncorpDate,
                MFIncorpCountry: share.shareholderProfile.MFIncorpCountry,
                MFDateOfBirth: share.shareholderProfile.MFDateOfBirth,
                MFFormerName: share.shareholderProfile.MFFormerName,
                MFBirthCountry: share.shareholderProfile.MFBirthCountry,
                MFNationality: share.shareholderProfile.MFNationality,
                MFRAAddress: share.shareholderProfile.MFRAAddress,
                MFROAddress: share.shareholderProfile.MFROAddress,
                MFRSAddress: share.shareholderProfile.MFRSAddress,
                MFStatusCode: share.shareholderProfile.MFStatusCode,
                MFStatus: share.shareholderProfile.MFStatus,
                MFProductionOffice: share.shareholderProfile.MFProductionOffice
            });
        }

        return res.status(200).json({
            "status": 200,
            "message": "We have received your confirmation to member information and will process the data to BVI Registry in due course if we haven't submit before."
        });

    } catch (error) {
        console.error('Error in process to confirm member data: ', error);
        return res.status(500).json({
            "status": 500,
            "error": 'Failed to confirm member information'
        });
    }
}

exports.createStockOrFundConfirmationLog = async function (req, res) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }
        
        // Determine if this is for stock or fund
        const isStockConfirmation = req.path.includes('/confirm-stock');
        
        // Get entity data
        const entity = await mem_EntitiesModel.findOne({
            where: { EntityLegacyID: company.code },
            raw: true
        });
        
        if (!entity) {
            return res.status(404).json({
                "status": 404,
                "error": 'Entity information not found'
            });
        }
        
        try {
            if (isStockConfirmation) {
                // Create stock confirmation in the stock history table
                await mem_EntitiesStockHistory.create({
                    EntityLegacyID: company.code,
                    EntityCode: entity.EntityCode || '',
                    EntityName: entity.EntityName || '',
                    EntityUniqueNr: entity.EntityUniqueNr,
                    ClientCode: entity.ClientCode || '',
                    ClientName: entity.ClientName || '',
                    ClientUniqueNr: entity.ClientUniqueNr,
                    // Stock-specific fields
                    STXName: entity.STXName || '',
                    STXTicker: entity.STXTicker || '',
                    STXJurisdiction: entity.STXJurisdiction || '',
                    STXRegulator: entity.STXRegulator || '',
                    STXListingDate: entity.STXListingDate || null,
                    IncorporationNumber: entity.IncorporationNumber || '',
                    IncorporationDate: entity.IncorporationDate || null,
                    JurisdictionCode: entity.JurisdictionCode || '',
                    Jurisdiction: entity.Jurisdiction || '',
                    EntityTypeCode: entity.EntityTypeCode || '',
                    EntityType: entity.EntityType || '',
                    EntityStatusCode: entity.EntityStatusCode || '',
                    EntityStatus: entity.EntityStatus || '',
                    EntitySubStatusCode: entity.EntitySubStatusCode || '',
                    EntitySubStatus: entity.EntitySubStatus || '',
                    ProductionOffice: entity.ProductionOffice || '',
                    // Confirmation fields
                    ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                    Status: DIRMEMBER_STATUS.CONFIRMED,
                    UserEmail: req.user.username,
                    CreatedAt: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                    UpdatedAt: moment.utc().format('YYYY-MM-DD HH:mm:ss')
                });
            } else {
                // Create fund confirmation in the fund history table
                await mem_EntitiesMutualFundHistory.create({
                    EntityLegacyID: company.code,
                    EntityCode: entity.EntityCode || '',
                    EntityName: entity.EntityName || '',
                    EntityUniqueNr: entity.EntityUniqueNr,
                    ClientCode: entity.ClientCode || '',
                    ClientName: entity.ClientName || '',
                    ClientUniqueNr: entity.ClientUniqueNr,
                    // Mutual Fund specific fields
                    IncorporationNumber: entity.IncorporationNumber || '',
                    IncorporationDate: entity.IncorporationDate || null,
                    JurisdictionCode: entity.JurisdictionCode || '',
                    Jurisdiction: entity.Jurisdiction || '',
                    EntityTypeCode: entity.EntityTypeCode || '',
                    EntityType: entity.EntityType || '',
                    ProductionOffice: entity.ProductionOffice || '',
                    EntityStatusCode: entity.EntityStatusCode || '',
                    EntityStatus: entity.EntityStatus || '',
                    EntitySubStatusCode: entity.EntitySubStatusCode || '',
                    EntitySubStatus: entity.EntitySubStatus || '',
                    // Business registration fields specific to funds
                    BusRegNr: entity.BusRegNr || '',
                    BusRegTypeCode: entity.BusRegTypeCode || '',
                    BusRegType: entity.BusRegType || '',
                    BusRegStartDate: entity.BusRegStartDate || null,
                    BusRegEndDate: entity.BusRegEndDate || null,
                    // Confirmation fields
                    ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                    Status: DIRMEMBER_STATUS.CONFIRMED,
                    UserEmail: req.user.username,
                    CreatedAt: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                    UpdatedAt: moment.utc().format('YYYY-MM-DD HH:mm:ss')
                });
            }
            
            return res.status(200).json({
                "status": 200,
                "message": `${isStockConfirmation ? 'Stock' : 'Mutual Fund'} information confirmed successfully`
            });
        } catch (error) {
            console.error(`Error in confirming ${isStockConfirmation ? 'Stock' : 'Mutual Fund'} information: `, error);
            return res.status(500).json({
                "status": 500,
                "error": `Failed to confirm ${isStockConfirmation ? 'Stock' : 'Mutual Fund'} information`
            });
        }
    } catch (error) {
        console.error('Error in process to confirm stock/fund data: ', error);
        return res.status(500).json({
            "status": 500,
            "error": 'Failed to confirm information'
        });
    }
}


exports.requestToUpdate = async function (req, res) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
        const data = req.body;

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }

        if (!data.changeType) {
            return res.status(400).json({
                "status": 400,
                "error": 'Please select a valid option'
            });
        }
        
        //Get director from mem_Directors table
        let type = TYPE_OF_DIRECTOR_DIR;
        let director = await mem_DirectorsModel.findOne({ 
            where: { 
                EntityLegacyID: company.code,
                UniqueRelationID: req.params.id, 
            },
            include: [{
                model: mem_MemberProfilesModel,
                as: 'directorProfile',
                required: false
            },{
                model: mem_EntitiesModel,
                as: 'entity',
                required: false
            }],
            raw: false 
        });
        
        if (!director){
            return res.status(404).json({
                "status": 404,
                "error": 'Director information not found'
            });
        }
        
        let emailTo;

        if (director.entity?.ProductionOffice === 'TBVI') {
            emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI
        } else if (director.entity?.ProductionOffice === 'THKO'){
            emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO
        } else if (director.entity?.ProductionOffice === 'TCYP'){
            emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP
        } else if (director.entity?.ProductionOffice === 'TPANVG'){
            emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG
        } else {
            emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO
        }

        const noProductionOffice = director.entity?.ProductionOffice !== 'THKO' && director.entity?.ProductionOffice !== 'TBVI' && director.entity?.ProductionOffice !== 'TCYP' && director.entity?.ProductionOffice !== 'TPANVG' ? true : false;
        
        // Determine if the director is licensed based on user input
        const isDirectorLicensed = data.isDirectorLicensed;
        // ! mem_DirectorsHistory table has a limit of 10 characters for EntityCode
        const entityCode = director.EntityCode ? director.EntityCode.substring(0, 10) : '';
        const directorData = {
            UniqueRelationID: director.UniqueRelationID,
            ClientCode: director.ClientCode,
            ClientName: director.ClientName,
            ClientUniqueNr: director?.ClientUniqueNr, 
            EntityCode: entityCode,
            EntityName: director.EntityName,
            EntityUniqueNr: director?.EntityUniqueNr,
            EntityLegacyID: director.EntityLegacyID,
            DirCode: director.DirCode,
            DirName: director.DirName,
            DirUniqueNr: director.DirUniqueNr,
            DirFileType: director.DirFileType,
            RelationType: director.RelationType,
            DirOfficerType: director.DirOfficerType,
            DirFromDate: director.DirFromDate,
            DirToDate: director.DirToDate,
            DirStatus: director.DirStatus || '',
            LicenseeEntityCode: director.LicenseeEntityCode,
            LicenseeEntityName: director.LicenseeEntityName,
            DirCapacityCode: director.DirCapacityCode,
            DirCapacity: director.DirCapacity,
            DirID: director.DirID,
            PreviousUniqueRelationId: director.PreviousUniqueRelationId,
            UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
            ConfirmedDate: null,
            Status: DIRMEMBER_STATUS.PENDING,
            UserEmail: req.user.username, 
            TypeOfUpdateRequest: data.changeType,
            UpdateRequestComments: data.changeReason || "",
            isLicensed: isDirectorLicensed
        };
        
        // Create request in mem_DirectorsHistory
        let requestUpdateLog = await mem_DirectorsHistoryModel.create(directorData);

        // Create corresponding record in mem_MemberProfilesHistory if a profile exists
        if (director.directorProfile && requestUpdateLog.Id) {
            try {
                const profileData = {
                    DirectorHistoryId: requestUpdateLog.Id,
                    MFCode: director.directorProfile.MFCode,
                    MFUniqueNr: director.directorProfile.MFUniqueNr,
                    MFName: director.directorProfile.MFName,
                    MFTypeCode: director.directorProfile.MFTypeCode,
                    MFLegacyID: director.directorProfile.MFLegacyID,
                    MFType: director.directorProfile.MFType,
                    MFIncropNr: director.directorProfile.MFIncropNr,
                    MFIncorpDate: director.directorProfile.MFIncorpDate,
                    MFIncorpCountry: director.directorProfile.MFIncorpCountry,
                    MFDateOfBirth: director.directorProfile.MFDateOfBirth,
                    MFFormerName: director.directorProfile.MFFormerName,
                    MFBirthCountry: director.directorProfile.MFBirthCountry,
                    MFNationality: director.directorProfile.MFNationality,
                    MFRAAddress: director.directorProfile.MFRAAddress,
                    MFROAddress: director.directorProfile.MFROAddress,
                    MFRSAddress: director.directorProfile.MFRSAddress,
                    MFStatusCode: director.directorProfile.MFStatusCode,
                    MFStatus: director.directorProfile.MFStatus,
                    MFProductionOffice: director.directorProfile.MFProductionOffice
                };
                
                await mem_MemberProfilesHistoryModel.create(profileData);
            } catch (error) {
                console.error("Error creating MemberProfilesHistory record:", error);
            }
        }

        const subject = `${process.env.EMAIL_SUBJECT_PREFIX || ''} ${company.name} – Request update for Portal`;

        if(requestUpdateLog.Id){
            // Get missing information
            let missingInformation = '';
            const listOfRequiredFields = getListFieldsToValidate(
                type === TYPE_OF_MEMBER, 
                director.DirFileType?.toLowerCase(), 
                director.DirOfficerType
            );

            const directorValues = director.toJSON();

            if (directorValues.directorProfile) {
                // Common fields
                directorValues.ServiceAddress = director.directorProfile.MFRSAddress;
                directorValues.ResidentialOrRegisteredAddress = director.directorProfile.MFRAAddress;
                
                // For individual directors
                if (directorValues.DirFileType?.toLowerCase() === "individual") {
                    directorValues.DateOfBirthOrIncorp = director.directorProfile.MFDateOfBirth;
                    directorValues.PlaceOfBirthOrIncorp = director.directorProfile.MFBirthCountry;
                    directorValues.Nationality = director.directorProfile.MFNationality;
                } 
                // For corporate directors - use the actual field names from the profile
                else {
                    directorValues.MFIncropNr = director.directorProfile.MFIncropNr;
                    directorValues.MFROAddress = director.directorProfile.MFROAddress;
                    directorValues.MFIncorpDate = director.directorProfile.MFIncorpDate;
                    directorValues.MFIncorpCountry = director.directorProfile.MFIncorpCountry;
                }
            }
            
            missingInformation = listOfRequiredFields
                .filter((item) => directorValues[item.field] == null || directorValues[item.field] == undefined || directorValues[item.field] === "")
                .map((item) => item.name)
                .join(', ');

            let email = MailFormatter.generateDirRequestUpdateEmail({
                "companyCode": director.EntityCode + " (" + company.code + ")",
                "companyName": director.EntityName + " (" + company.name + ")",
                "mcc": director.ClientCode + " (" + company.masterclientcode + ")",
                "directorCode": director.DirCode || 'N/A',
                "requestor": requestUpdateLog.UserEmail,
                "relationType": directorValues.DirFileType?.toLowerCase() === "individual" ? 'Individual' : 'Corporate',
                "requestType": requestUpdateLog.TypeOfUpdateRequest,
                "comment": requestUpdateLog.UpdateRequestComments || 'N/A',
                "position": type,
                "licensedDirector": (isDirectorLicensed || director.LicenseeEntityName || director.DirCapacity) ? 'Yes' : 'No',
                "missingInformation": missingInformation || 'N/A'
            });
            
            let emailResponse = await MailController.asyncSend(
                emailTo,
                noProductionOffice ? '(!Production office unknown) ' + subject : subject,
                email.textString,
                email.htmlString
            );
            
            if (emailResponse.error) {
                console.error("Send director email error: ", emailResponse);
            }

            return res.status(200).json({ "status": 200, "message": "Request an update has been created successfully" });
        }else{
            return res.status(500).json({ "status": 500, "message": "There was an error creating the request" });
        }
        
    } catch (e) {
        console.error("Error creating request an update: ", e);
        return res.status(500).json({
            "status": 500,
            "error": 'Internal server error'
        });
    }
}


exports.requestMemberToUpdate = async function (req, res) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
        const data = req.body;

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }

        if (!data.changeType) {
            return res.status(400).json({
                "status": 400,
                "error": 'Please select a valid option'
            });
        }

        let memberProfile = await mem_MemberProfilesModel.findOne({
            where: {
                MFUniqueNr: req.params.id,
            },
            raw: false
        });

        if (!memberProfile) {
            return res.status(404).json({
                "status": 404,
                "error": 'Member information not found'
            });
        }

        // Get ALL share records for this shareholder
        let allShareholderRows = await mem_ShareholdersModel.findAll({
            where: {
                EntityLegacyID: company.code,
                SHUniqueNr: req.params.id
            },
            raw: false
        });

        if (!allShareholderRows || allShareholderRows.length === 0) {
            return res.status(404).json({
                "status": 404,
                "error": 'No shareholder records found'
            });
        }

        // Get all nominator profiles (linked through MemberCode)
        const nominatorProfilesByMemberCode = {};
        for (const share of allShareholderRows) {
            if (share.MemberCode) {
                if (!nominatorProfilesByMemberCode[share.MemberCode]) {
                    const nominatorProfile = await mem_MemberProfilesModel.findOne({
                        where: {
                            MFCode: share.MemberCode
                        },
                        raw: false
                    });
                    
                    if (nominatorProfile) {
                        nominatorProfilesByMemberCode[share.MemberCode] = {
                            ...nominatorProfile.dataValues,
                            isCorporate: share.MemberFileTypeCode !== "I"
                        };
                    }
                }
            }
        }

        // For each row in mem_Shareholders, create a history record
        const historyRecords = [];
        
        for (const share of allShareholderRows) {
            // Create history record for this share
            const historyRecord = await mem_ShareholdersHistoryModel.create({
                UniqueRelationID: share.UniqueRelationID,
                ClientCode: share.ClientCode,
                ClientName: share.ClientName,
                ClientUniqueNr: share.ClientUniqueNr,
                EntityCode: share.EntityCode,
                EntityName: share.EntityName,
                EntityUniqueNr: share.EntityUniqueNr,
                EntityLegacyID: share.EntityLegacyID,
                RelationType: share.RelationType,
                SHCode: share.SHCode,
                SHName: share.SHName,
                SHUniqueNr: share.SHUniqueNr,
                SHFileTypeCode: share.SHFileTypeCode,
                SHFileType: share.SHFileType,
                MemberTypeCode: share.MemberTypeCode,
                MemberType: share.MemberType,
                MemberCode: share.MemberCode,
                MemberUniqueNr: share.MemberUniqueNr,
                MemberName: share.MemberName,
                MemberFileTypeCode: share.MemberFileTypeCode,
                MemberFileType: share.MemberFileType,
                MemberDateStart: share.MemberDateStart,
                MemberDateEnd: share.MemberDateEnd,
                ShareTypeCode: share.ShareTypeCode,
                ShareClassName: share.ShareClassName,
                SHVotingRights: share.SHVotingRights,
                ShareIssueDate: share.ShareIssueDate,
                SHCertNr: share.SHCertNr,
                NrOfShares: share.NrOfShares,
                SHAddress: share.SHAddress || '',
                BenOwnerCode: share.BenOwnerCode,
                BenOwner: share.BenOwner,
                BenOwnerCertNr: share.BenOwnerCertNr,
                ShareholderID: share.ShareholderID,
                UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                Status: DIRMEMBER_STATUS.PENDING,
                UserEmail: req.user.username,
                TypeOfUpdateRequest: data.changeType,
                UpdateRequestComments: data.changeReason || "",
                hasNomineeArrangement: data.hasNomineeArrangement
            });
            
            historyRecords.push(historyRecord);

            await mem_MemberProfilesHistoryModel.create({
                ShareholderHistoryId: historyRecord.Id,
                MFCode: memberProfile.MFCode,
                MFUniqueNr: memberProfile.MFUniqueNr,
                MFName: memberProfile.MFName,
                MFTypeCode: memberProfile.MFTypeCode,
                MFLegacyID: memberProfile.MFLegacyID,
                MFType: memberProfile.MFType,
                MFIncropNr: memberProfile.MFIncropNr,
                MFIncorpDate: memberProfile.MFIncorpDate,
                MFIncorpCountry: memberProfile.MFIncorpCountry,
                MFDateOfBirth: memberProfile.MFDateOfBirth,
                MFFormerName: memberProfile.MFFormerName,
                MFBirthCountry: memberProfile.MFBirthCountry,
                MFNationality: memberProfile.MFNationality,
                MFRAAddress: memberProfile.MFRAAddress,
                MFROAddress: memberProfile.MFROAddress,
                MFRSAddress: memberProfile.MFRSAddress,
                MFStatusCode: memberProfile.MFStatusCode,
                MFStatus: memberProfile.MFStatus,
                MFProductionOffice: memberProfile.MFProductionOffice
            });

            // If this share has a nominator, create history for that nominator's profile
            if (share.MemberCode && nominatorProfilesByMemberCode[share.MemberCode]) {
                const nominatorProfile = nominatorProfilesByMemberCode[share.MemberCode];
                
                await mem_MemberProfilesHistoryModel.create({
                    ShareholderHistoryId: historyRecord.Id,
                    MFCode: nominatorProfile.MFCode,
                    MFUniqueNr: nominatorProfile.MFUniqueNr,
                    MFName: nominatorProfile.MFName,
                    MFTypeCode: nominatorProfile.MFTypeCode,
                    MFLegacyID: nominatorProfile.MFLegacyID,
                    MFType: nominatorProfile.MFType,
                    MFIncropNr: nominatorProfile.MFIncropNr,
                    MFIncorpDate: nominatorProfile.MFIncorpDate,
                    MFIncorpCountry: nominatorProfile.MFIncorpCountry,
                    MFDateOfBirth: nominatorProfile.MFDateOfBirth,
                    MFFormerName: nominatorProfile.MFFormerName,
                    MFBirthCountry: nominatorProfile.MFBirthCountry,
                    MFNationality: nominatorProfile.MFNationality,
                    MFRAAddress: nominatorProfile.MFRAAddress,
                    MFROAddress: nominatorProfile.MFROAddress,
                    MFRSAddress: nominatorProfile.MFRSAddress,
                    MFStatusCode: nominatorProfile.MFStatusCode,
                    MFStatus: nominatorProfile.MFStatus,
                    MFProductionOffice: nominatorProfile.MFProductionOffice
                });
            }
        }

        // Send email notification
        let emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
        
        // Get entity to determine production office
        const entity = await mem_EntitiesModel?.findOne({
            where: {
                EntityLegacyID: company.code,
            },
            raw: true
        });
        let noProductionOffice = false;
        // Set correct production office
        if (entity && entity.ProductionOffice) {
            if (entity.ProductionOffice === 'TBVI') {
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI;
            } else if (entity.ProductionOffice === 'THKO'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
            } else if (entity.ProductionOffice === 'TCYP'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP;
            } else if (entity.ProductionOffice === 'TPANVG'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG;
            }
        } else {
            noProductionOffice = true;
        }

        // Get missing information
        const listOfRequiredFieldsCorporate = getListFieldsToValidate(true, null, "Corporate");
        const listOfRequiredFieldsIndividual = getListFieldsToValidate(true, null, "Individual");

        // Validate member profile depending on type and all nominator profiles, if any. Only add "Nominator Details" if missing values
        const missingInformation = [];
        const requiredFields = allShareholderRows[0].SHFileTypeCode === "I" ? listOfRequiredFieldsIndividual : listOfRequiredFieldsCorporate;

        const missingValues = requiredFields.filter((item) => {
            const fieldValue = memberProfile[item.field];
            return !fieldValue; // Checks for null, undefined, empty string
        }).map((item) => item.name);

        if (missingValues.length) {
            missingInformation.push(...missingValues);
        }

        for (const key in nominatorProfilesByMemberCode) {
            const nominatorProfile = nominatorProfilesByMemberCode[key];
            const requiredFieldsNominator = nominatorProfile.isCorporate ? listOfRequiredFieldsCorporate :listOfRequiredFieldsIndividual;
            const missingNominatorValues = requiredFieldsNominator.filter((item) => {
                const fieldValue = nominatorProfile[item.field];
                return !fieldValue; // Checks for null, undefined, empty string
            }).map((item) => item.name);

            if (missingNominatorValues.length && !missingInformation.includes("Nominator Details")) {
                missingInformation.push("Nominator Details");
            }
        }

        // Send email notification
        const subject = `${process.env.EMAIL_SUBJECT_PREFIX || ''} ${company.name} – Request update for Portal`;

        let email = MailFormatter.generateMemberRequestUpdateEmail({
            "companyCode": allShareholderRows[0].EntityCode + " (" + company.code + ")",
            "companyName": allShareholderRows[0].EntityName + " (" + company.name + ")",
            "memberCode": allShareholderRows[0].SHCode || 'N/A',
            "mcc": allShareholderRows[0].ClientCode + " (" + company.masterclientcode + ")",
            "requestor": req.user.username,
            "requestType": data.changeType,
            "comment": data.changeReason || 'N/A',
            "position": "Member",
            "relationType": allShareholderRows[0].SHFileTypeCode === 'I' ? 'Individual' : 'Corporate',
            "nomineeArrangement": (data.hasNomineeArrangement || Object.keys(nominatorProfilesByMemberCode).length > 0) ? 'Yes' : 'No',
            "missingInformation": missingInformation.join(', ') || 'N/A'
        });

        let emailResponse = await MailController.asyncSend(
            emailTo,
            noProductionOffice ? '(!Production office unknown) ' + subject : subject,
            email.textString,
            email.htmlString
        );

        if (emailResponse.error) {
            console.error("Send member email error: ", emailResponse);
        }

        return res.status(200).json({
            "status": 200,
            "message": "Request an update has been created successfully"
        });

    } catch (error) {
        console.error("Error creating request an update for member: ", error);
        return res.status(500).json({
            "status": 500,
            "error": 'Internal server error'
        });
    }
}

exports.requestJointMemberToUpdate = async function (req, res) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
        const data = req.body;

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }

        if (!data.changeType) {
            return res.status(400).json({
                "status": 400,
                "error": 'Please select a valid option'
            });
        }

        // Get ALL share records for this shareholder
        let allShareholderRows = await mem_ShareholdersModel.findAll({
            where: {
                EntityLegacyID: company.code,
                SHCertNr: req.params.certNr.split("-")
            },
            include: [{
                model: mem_MemberProfilesModel,
                as: 'shareholderProfile',
                required: false
            }],
            raw: false
        });

        if (!allShareholderRows || allShareholderRows.length === 0) {
            return res.status(404).json({
                "status": 404,
                "error": 'No shareholder records found'
            });
        }

        const listOfRequiredFieldsCorporate = getListFieldsToValidate(true, null, "Corporate");
        const listOfRequiredFieldsIndividual = getListFieldsToValidate(true, null, "Individual");

        let missingValues = {};
        for (const share of allShareholderRows) {
            // Create history record for this share
            const historyRecord = await mem_ShareholdersHistoryModel.create({
                UniqueRelationID: share.UniqueRelationID,
                ClientCode: share.ClientCode,
                ClientName: share.ClientName,
                ClientUniqueNr: share.ClientUniqueNr,
                EntityCode: share.EntityCode,
                EntityName: share.EntityName,
                EntityUniqueNr: share.EntityUniqueNr,
                EntityLegacyID: share.EntityLegacyID,
                RelationType: share.RelationType,
                SHCode: share.SHCode,
                SHName: share.SHName,
                SHUniqueNr: share.SHUniqueNr,
                SHFileTypeCode: share.SHFileTypeCode,
                SHFileType: share.SHFileType,
                MemberTypeCode: share.MemberTypeCode,
                MemberType: share.MemberType,
                MemberCode: share.MemberCode,
                MemberUniqueNr: share.MemberUniqueNr,
                MemberName: share.MemberName,
                MemberFileTypeCode: share.MemberFileTypeCode,
                MemberFileType: share.MemberFileType,
                MemberDateStart: share.MemberDateStart,
                MemberDateEnd: share.MemberDateEnd,
                ShareTypeCode: share.ShareTypeCode,
                ShareClassName: share.ShareClassName,
                SHVotingRights: share.SHVotingRights,
                ShareIssueDate: share.ShareIssueDate,
                SHCertNr: share.SHCertNr,
                NrOfShares: share.NrOfShares,
                SHAddress: share.SHAddress || '',
                BenOwnerCode: share.BenOwnerCode,
                BenOwner: share.BenOwner,
                BenOwnerCertNr: share.BenOwnerCertNr,
                ShareholderID: share.ShareholderID,
                UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                Status: DIRMEMBER_STATUS.PENDING,
                UserEmail: req.user.username,
                TypeOfUpdateRequest: data.changeType,
                UpdateRequestComments: data.changeReason || "",
                hasNomineeArrangement: data.hasNomineeArrangement
            });
            
            await mem_MemberProfilesHistoryModel.create({
                ShareholderHistoryId: historyRecord.Id,
                MFCode: share.shareholderProfile.MFCode,
                MFUniqueNr: share.shareholderProfile.MFUniqueNr,
                MFName: share.shareholderProfile.MFName,
                MFTypeCode: share.shareholderProfile.MFTypeCode,
                MFLegacyID: share.shareholderProfile.MFLegacyID,
                MFType: share.shareholderProfile.MFType,
                MFIncropNr: share.shareholderProfile.MFIncropNr,
                MFIncorpDate: share.shareholderProfile.MFIncorpDate,
                MFIncorpCountry: share.shareholderProfile.MFIncorpCountry,
                MFDateOfBirth: share.shareholderProfile.MFDateOfBirth,
                MFFormerName: share.shareholderProfile.MFFormerName,
                MFBirthCountry: share.shareholderProfile.MFBirthCountry,
                MFNationality: share.shareholderProfile.MFNationality,
                MFRAAddress: share.shareholderProfile.MFRAAddress,
                MFROAddress: share.shareholderProfile.MFROAddress,
                MFRSAddress: share.shareholderProfile.MFRSAddress,
                MFStatusCode: share.shareholderProfile.MFStatusCode,
                MFStatus: share.shareholderProfile.MFStatus,
                MFProductionOffice: share.shareholderProfile.MFProductionOffice
            });

            // Check missing values
            const requiredFields = share.SHFileTypeCode === "I" ? listOfRequiredFieldsIndividual : listOfRequiredFieldsCorporate;
            missingValues[share.SHCode] = requiredFields.filter((item) => {
                const fieldValue = share[item.field];
                const profileFieldValue = share.shareholderProfile?.[item.field];
                return !fieldValue && !profileFieldValue; // Checks for null, undefined, empty string
            }).map((item) => item.name);
        }
    
        let emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
        
        // Get entity to determine production office
        const entity = await mem_EntitiesModel?.findOne({
            where: {
                EntityLegacyID: company.code,
            },
            raw: true
        });
        let noProductionOffice = false;
        // Set correct production office
        if (entity && entity.ProductionOffice) {
            if (entity.ProductionOffice === 'TBVI') {
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI;
            } else if (entity.ProductionOffice === 'THKO'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
            } else if (entity.ProductionOffice === 'TCYP'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP;
            } else if (entity.ProductionOffice === 'TPANVG'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG;
            }
        } else {
            noProductionOffice = true;
        }

        // Send email notification
        const subject = `${process.env.EMAIL_SUBJECT_PREFIX || ''} ${company.name} – Request update for Portal`;

        let missingInformationText = "";
        for (const shareCode in missingValues) {
            if (missingValues[shareCode].length > 0) {
                missingInformationText += `${shareCode}: ${missingValues[shareCode].join(', ')}. `;
            }
        }

        const shNumbers = [...new Set(allShareholderRows.map((e) => e.SHCertNr))];

        let email = MailFormatter.generateMemberRequestUpdateEmail({
            "companyCode": allShareholderRows[0].EntityCode + " (" + company.code + ")",
            "companyName": allShareholderRows[0].EntityName + " (" + company.name + ")",
            "memberCode": shNumbers.join(', '),
            "mcc": allShareholderRows[0].ClientCode + " (" + company.masterclientcode + ")",
            "requestor": req.user.username,
            "requestType": data.changeType,
            "comment": data.changeReason || 'N/A',
            "position": "Member",
            "relationType": "Joint",
            "nomineeArrangement": data.hasNomineeArrangement ? 'Yes' : 'No',
            "missingInformation": missingInformationText ? missingInformationText :'N/A'
        });

        let emailResponse = await MailController.asyncSend(
            emailTo,
            noProductionOffice ? '(!Production office unknown) ' + subject : subject,
            email.textString,
            email.htmlString
        );

        if (emailResponse.error) {
            console.error("Send member email error: ", emailResponse);
        }

        return res.status(200).json({
            "status": 200,
            "message": "Request an update has been created successfully"
        });

    } catch (error) {
        console.error("Error creating request an update for member: ", error);
        return res.status(500).json({
            "status": 500,
            "error": 'Internal server error'
        });
    }
}


exports.requestStockOrFundToUpdate = async function (req, res) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
        const data = req.body;

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }

        if (!data.changeType) {
            return res.status(400).json({
                "status": 400,
                "error": 'Please select a valid option'
            });
        }
        
        // Determine if this is for stock or fund
        const isStockUpdate = req.path.includes('/request-stock-update');
        const updateType = isStockUpdate ? 'STOCK' : 'FUND';
        
        // Get entity data
        const entity = await mem_EntitiesModel.findOne({
            where: { EntityLegacyID: company.code },
            raw: true
        });
        
        if (!entity) {
            return res.status(404).json({
                "status": 404,
                "error": 'Entity information not found'
            });
        }
        
        try {
            if(updateType === 'STOCK') {
                // Create log entry with all relevant stock fields
                await mem_EntitiesStockHistory.create({
                    EntityLegacyID: company.code,
                    EntityCode: entity.EntityCode || '',
                    EntityName: entity.EntityName || '',
                    EntityUniqueNr: entity.EntityUniqueNr,
                    ClientCode: entity.ClientCode || '',
                    ClientName: entity.ClientName || '',
                    ClientUniqueNr: entity.ClientUniqueNr,
                    // Stock-specific fields
                    STXName: entity.STXName || '',
                    STXTicker: entity.STXTicker || '',
                    STXJurisdiction: entity.STXJurisdiction || '',
                    STXRegulator: entity.STXRegulator || '',
                    STXListingDate: entity.STXListingDate || null,
                    IncorporationNumber: entity.IncorporationNumber || '',
                    IncorporationDate: entity.IncorporationDate || null,
                    JurisdictionCode: entity.JurisdictionCode || '',
                    Jurisdiction: entity.Jurisdiction || '',
                    EntityTypeCode: entity.EntityTypeCode || '',
                    EntityType: entity.EntityType || '',
                    EntityStatusCode: entity.EntityStatusCode || '',
                    EntityStatus: entity.EntityStatus || '',
                    EntitySubStatusCode: entity.EntitySubStatusCode || '',
                    EntitySubStatus: entity.EntitySubStatus || '',
                    ProductionOffice: entity.ProductionOffice || '',
                    // Request fields
                    UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                    Status: DIRMEMBER_STATUS.PENDING,
                    UserEmail: req.user.username,
                    TypeOfUpdateRequest: data.changeType,
                    UpdateRequestComments: data.changeReason || "",
                    ConfirmationType: updateType,
                    RelationType: updateType,
                    MemberTypeCode: updateType === 'STOCK' ? 'STX' : 'BRG',
                    MemberType: updateType,
                    SHName: entity.EntityName || '',
                    CreatedAt: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                    UpdatedAt: moment.utc().format('YYYY-MM-DD HH:mm:ss')
                });
            } else if (updateType === 'FUND') {                
                // Create log entry with all relevant mutual fund fields
                await mem_EntitiesMutualFundHistory.create({
                    EntityLegacyID: company.code,
                    EntityCode: entity.EntityCode || '',
                    EntityName: entity.EntityName || '',
                    EntityUniqueNr: entity.EntityUniqueNr,
                    ClientCode: entity.ClientCode || '',
                    ClientName: entity.ClientName || '',
                    ClientUniqueNr: entity.ClientUniqueNr,
                    // Mutual Fund specific fields
                    IncorporationNumber: entity.IncorporationNumber || '',
                    IncorporationDate: entity.IncorporationDate || null,
                    JurisdictionCode: entity.JurisdictionCode || '',
                    Jurisdiction: entity.Jurisdiction || '',
                    EntityTypeCode: entity.EntityTypeCode || '',
                    EntityType: entity.EntityType || '',
                    ProductionOffice: entity.ProductionOffice || '',
                    EntityStatusCode: entity.EntityStatusCode || '',
                    EntityStatus: entity.EntityStatus || '',
                    EntitySubStatusCode: entity.EntitySubStatusCode || '',
                    EntitySubStatus: entity.EntitySubStatus || '',
                    // Business registration fields specific to funds
                    BusRegNr: entity.BusRegNr || '',
                    BusRegTypeCode: entity.BusRegTypeCode || '',
                    BusRegType: entity.BusRegType || '',
                    BusRegStartDate: entity.BusRegStartDate || null,
                    BusRegEndDate: entity.BusRegEndDate || null,
                    // Request fields
                    UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                    Status: DIRMEMBER_STATUS.PENDING,
                    UserEmail: req.user.username,
                    TypeOfUpdateRequest: data.changeType,
                    UpdateRequestComments: data.changeReason || "",
                    ConfirmationType: updateType,
                    RelationType: updateType,
                    MemberTypeCode: updateType === 'STOCK' ? 'STX' : 'BRG',
                    MemberType: updateType,
                    SHName: entity.EntityName || '',
                    CreatedAt: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                    UpdatedAt: moment.utc().format('YYYY-MM-DD HH:mm:ss')
                })
            }
            
            // Determine email recipient based on production office
            // let emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO; // Default email
            let emailTo = process.env.REQUEST_UPDATE_EMAIL_TO;

            const subject = `${process.env.EMAIL_SUBJECT_PREFIX || ''} ${company.name} – Request update for ${updateType}`;
            
            let email = MailFormatter.generateStockOrFundRequestUpdateEmail({
                "companyCode": entity.EntityCode + " (" + company.code + ")",
                "companyName": entity.EntityName + " (" + company.name + ")",
                "mcc": entity.ClientCode + " (" + company.masterclientcode + ")",
                "requestor": req.user.username,
                "requestType": data.changeType,
                "comment": data.changeReason || 'N/A',
                "relationType": updateType,
                "missingInformation": 'N/A'
            });
            
            let emailResponse = await MailController.asyncSend(
                emailTo,
                subject,
                email.textString,
                email.htmlString
            );
            
            if (emailResponse.error) {
                console.error(`Send ${updateType} email error: `, emailResponse);
            }
            
            return res.status(200).json({
                "status": 200,
                "message": `${updateType} update request created successfully`
            });
        } catch (error) {
            console.error(`Error in creating ${updateType} update request: `, error);
            return res.status(500).json({
                "status": 500,
                "error": `Failed to create ${updateType} update request`
            });
        }
    } catch (e) {
        console.error("Error creating request an update for stock/fund: ", e);
        return res.status(500).json({
            "status": 500,
            "error": 'Internal server error'
        });
    }
}


exports.requestToAssistance = async function (req, res) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }

        // Determine the request type based on the URL path
        let requestType;
        let position;

        if (req.originalUrl.includes('/directors/')) {
            requestType = "No Director";
            position = "Director";
        } else if (req.originalUrl.includes('/members/')) {
            requestType = "No Member";
            position = "Member";
        } else {
            // Default to director if path is unclear
            requestType = "No Director";
            position = "Director";
        }

        let emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
        
        // Get entity to determine production office
        const entity = await mem_EntitiesModel?.findOne({
            where: {
                EntityLegacyID: company.code,
            },
            raw: true
        });
        let noProductionOffice = false;
        // Set correct production office
        if (entity && entity.ProductionOffice) {
            if (entity.ProductionOffice === 'TBVI') {
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI;
            } else if (entity.ProductionOffice === 'THKO'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
            } else if (entity.ProductionOffice === 'TCYP'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP;
            } else if (entity.ProductionOffice === 'TPANVG'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG;
            }
        } else {
            noProductionOffice = true;
        }

        // Create email subject
        const subject = `${process.env.EMAIL_SUBJECT_PREFIX || ''} ${company.name} – No ${position} found in the portal`;
        
        // Prepare email data
        const emailData = {
            "companyCode": entity ? `${entity.EntityCode} (${company.code})` : company.code,
            "companyName": entity ? `${entity.EntityName} (${company.name})` : company.name,
            "mcc": entity ? `${entity.ClientCode} (${company.masterclientcode})` : company.masterclientcode,
            "requestor": req.user.username,
            "requestType": requestType,
        };
        
        // Send the email
        let email = MailFormatter.generateDirMemRequestAssistanceEmail(emailData);
        let emailResponse = await MailController.asyncSend(            
            emailTo,
            noProductionOffice ? '(!Production office unknown) ' + subject : subject,
            email.textString,
            email.htmlString
        );
        
        if (emailResponse.error) {
            console.error("Send director email error: ", emailResponse);
            return res.status(500).json({ "status": 500, "message": "There was an error sending the request for assistance" });
        }

        return res.status(200).json({ "status": 200, "message": "We have received your request for assistance. A Trident Trust Representative will be in touch shortly." });
    } catch (e) {
        console.error("Error creating request assistance: ", e);
        return res.status(500).json({
            "status": 500,
            "error": 'Internal server error'
        });
    }
}