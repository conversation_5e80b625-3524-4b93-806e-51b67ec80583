const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
    const Shareholders = sequelize.define('mem_Shareholders', {
        UniqueRelationID: {
            type: Sequelize.STRING(42),
            allowNull: false,
            primaryKey: true
        },
        ClientCode: Sequelize.STRING(10),
        ClientName: Sequelize.STRING(356),
        ClientUniqueNr: {
            type: Sequelize.INTEGER,
            allowNull: false
        },
        EntityCode: {
            type: Sequelize.STRING(10),
            allowNull: false
        },
        EntityName: Sequelize.STRING(356),
        EntityUniqueNr: {
            type: Sequelize.INTEGER,
            allowNull: false
        },
        EntityLegacyID: Sequelize.STRING(30),
        RelationType: {
            type: Sequelize.STRING(20),
            allowNull: false
        },
        SHCode: Sequelize.STRING(10),
        SHName: Sequelize.STRING(356),
        SHUniqueNr: {
            type: Sequelize.INTEGER,
            allowNull: true
        },
        SHFileTypeCode: Sequelize.STRING(1),
        SHFileType: Sequelize.STRING(50),
        MemberTypeCode: {
            type: Sequelize.STRING(20),
            allowNull: false
        },
        MemberType: {
            type: Sequelize.STRING(255),
            allowNull: false
        },
        MemberCode: Sequelize.STRING(10),
        MemberUniqueNr: {
            type: Sequelize.INTEGER,
            allowNull: true
        },
        MemberName: Sequelize.STRING(356),
        MemberFileTypeCode: Sequelize.STRING(1),
        MemberFileType: Sequelize.STRING(50),
        MemberDateStart: {
            type: Sequelize.DATE,
            allowNull: true,
            get() {
                const d = this.getDataValue('MemberDateStart');
                return d ? moment.utc(d).format('YYYY-MM-DD') : null;
            }
        },
        MemberDateEnd: {
            type: Sequelize.DATE,
            allowNull: true,
            get() {
                const d = this.getDataValue('MemberDateEnd');
                return d ? moment.utc(d).format('YYYY-MM-DD') : null;
            }
        },
        ShareTypeCode: Sequelize.STRING(2),
        ShareClassName: Sequelize.STRING(100),
        SHVotingRights: Sequelize.STRING(16),
        ShareIssueDate: {
            type: Sequelize.DATE,
            allowNull: true,
            get() {
                const d = this.getDataValue('ShareIssueDate');
                return d ? moment.utc(d).format('YYYY-MM-DD') : null;
            }
        },
        SHCertNr: {
            type: Sequelize.STRING(10),
            allowNull: false,
            primaryKey: true
        },
        NrOfShares: {
            type: Sequelize.DECIMAL,
            allowNull: true
        },
        SHAddress: {
            type: Sequelize.TEXT,
            allowNull: false
        },
        BenOwnerCode: Sequelize.STRING(10),
        BenOwner: Sequelize.STRING(356),
        BenOwnerCertNr: Sequelize.STRING(10),
        ShareholderID: Sequelize.STRING(100)
    }, {
        sequelize,
        tableName: 'mem_Shareholders',
        schema: 'dbo',
        timestamps: false
    });

    Shareholders.associate = function (models) {
        // Define association between mem_Shareholders and mem_MemberProfiles
        Shareholders.belongsTo(models.mem_MemberProfiles, {
            foreignKey: 'SHUniqueNr',  // Foreign key in mem_Shareholders
            targetKey: 'MFUniqueNr',    // Target key in mem_MemberProfiles
            as: 'shareholderProfile'       // Alias for the association
        });

        Shareholders.belongsTo(models.mem_MemberProfiles, {
            foreignKey: 'MemberCode',  // Foreign key in mem_Shareholders
            targetKey: 'MFCode',    // Target key in mem_MemberProfiles
            as: 'nominator'       // Alias for the association
        });

        // Define association between mem_Shareholders and mem_ShareholdersHistory
        Shareholders.hasMany(models.mem_ShareholdersHistory, {
            foreignKey: 'UniqueRelationID',
            sourceKey: 'UniqueRelationID',
            as: 'historyRecords'
        });
    };

    return Shareholders;
};
