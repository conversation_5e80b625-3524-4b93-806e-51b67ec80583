const express = require('express');
const router = express.Router({mergeParams: true});
const sessionUtils = require('../utils/sessionUtils');

// Require controller modules.
const directorAndMemberController = require('../controllers/directorAndMemberController');

// Director specific routes
router.get('/', ensureAuthenticated, directorAndMemberController.listDirectorAndMemberCompanies);
router.get('/:code/directors', ensureAuthenticated, directorAndMemberController.getDirectorEntries);
router.post('/:code/directors/:id/confirm', ensureAuthenticated, directorAndMemberController.createDirectorConfirmationLog);
router.post('/:code/directors/:id/request-update', ensureAuthenticated, directorAndMemberController.requestToUpdate);
router.post('/:code/directors/request-assistance', ensureAuthenticated, directorAndMemberController.requestToAssistance);
router.get('/:code/directors/:id/details', ensureAuthenticated, directorAndMemberController.getDirectorDetails);

// Member specific routes
router.get('/:code/members', ensureAuthenticated, directorAndMemberController.getMemberEntries);
router.post('/:code/members/request-assistance', ensureAuthenticated, directorAndMemberController.requestToAssistance);
router.post('/:code/members/:id/request-update', ensureAuthenticated, directorAndMemberController.requestMemberToUpdate); 
router.post('/:code/members/:id/confirm', ensureAuthenticated, directorAndMemberController.createMemberConfirmationLog);
router.get('/:code/members/joint/:certNr/details', ensureAuthenticated, directorAndMemberController.getJointMemberDetails);
router.post('/:code/members/joint/:certNr/request-update', ensureAuthenticated, directorAndMemberController.requestJointMemberToUpdate);
router.post('/:code/members/joint/:certNr/confirm', ensureAuthenticated, directorAndMemberController.createJointMemberConfirmationLog);
router.get('/:code/members/:id/details', ensureAuthenticated, directorAndMemberController.getMemberDetails);

// Stock and fund specific routes
router.post('/:code/request-stock-update', ensureAuthenticated, directorAndMemberController.requestStockOrFundToUpdate);
router.post('/:code/request-mutual-fund-update', ensureAuthenticated, directorAndMemberController.requestStockOrFundToUpdate);
router.post('/:code/confirm-stock', ensureAuthenticated, directorAndMemberController.createStockOrFundConfirmationLog);
router.post('/:code/confirm-fund', ensureAuthenticated, directorAndMemberController.createStockOrFundConfirmationLog);

module.exports = router;

function ensureAuthenticated(req, res, next) {
  if ((req.user && req.session.id === req.user.sessionId) && req.session.auth2fa) {
    next();
  } else if ((req.user && req.session.id === req.user.sessionId) && !req.session.auth2fa) {
    if (req.user.secret_2fa) {
      res.redirect('/users/2fa-code');
    } else {
      res.redirect('/users/2fa-setup');
    }
  } else {
    req.logout(function (err) {
      if (err) { return next(err) }
      
      req.session.destroy(function () {
        // cannot access session here
        sessionUtils.onSessionDestroyed(req, res);
      });
    });
  }
}
