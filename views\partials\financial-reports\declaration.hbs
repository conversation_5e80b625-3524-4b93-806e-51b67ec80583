<div class="col-lg-12">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <h4>Declaration and Confirmation Page</h4>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 ml-3">
                    I confirm that:
                </div>
            </div>

            {{#ifEquals report.version '1.0'}}
                <div class="pl-3 mt-2">
                    <div class="row">
                        <div class="col-md-12 custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="information" name="information" {{#ifEquals
                                report.declaration.information true}} checked {{/ifEquals}} required>
                            <label class="custom-control-label" for="information">
                                The details presented in this portal, along with any document(s) submitted while completing this
                                accounting return, are, to the best of my knowledge and belief, accurate and true. </label>
                        </div>
                    </div>
                    <div id="declarationAssetsLiabilities" class="row {{#ifEquals report.reportDetails.isExemptCompany true}}
                        hide-element {{/ifEquals}}">
                        <div class="col-md-12 custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="assetsLiabilities" name="assetsLiabilities"
                                {{#ifEquals report.declaration.assetsLiabilities true}} checked {{/ifEquals}} required>
                            <label class="custom-control-label" for="assetsLiabilities">
                                Except for the assets and liabilities explicitly outlined in the form, the Company, to the best of my
                                knowledge, does not possess any additional assets or have any other liabilities.</label>
                        </div>
                    </div>
                    <div class="row ">
                        <div class="col-md-12 custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="declarationAmount" name="declarationAmount" required
                                {{#ifEquals report.declaration.declarationAmount true}} checked {{/ifEquals}}>
                            <label class="custom-control-label" for="declarationAmount">The assets allocated to the Company and the
                                funds utilized for all Trident's services originate from
                                lawful sources. Additionally,
                                the submission fee of US$<span id="paymentAmountText">
                                    {{#ifCond report.reportDetails.serviceType '===' "self-service-complete" }}
                                    {{decimalValue report.companyData.accountingRecordsModule.selfServiceCompleteAnnualReturnAmount}}
                                    {{/ifCond}}
                
                                    {{#ifCond report.reportDetails.serviceType '===' "self-service-prepare" }}
                                    {{decimalValue report.companyData.accountingRecordsModule.selfServicePrepareAnnualReturnAmount}}
                                    {{/ifCond}}
                
                                    {{#ifCond report.reportDetails.serviceType '===' "trident-service-complete" }}
                                    {{decimalValue report.companyData.accountingRecordsModule.tridentServiceCompleteAnnualReturnAmount}}
                                    {{/ifCond}}
                
                                    {{#ifCond report.reportDetails.serviceType '===' "trident-service-drop" }}
                                    {{decimalValue report.companyData.accountingRecordsModule.tridentServiceDropAccountingRecordsAmount}}
                                    {{/ifCond}}
                
                                    {{#ifCond report.reportDetails.isExemptCompany '===' true}}190{{/ifCond}}
                                </span> must be paid to fulfill the submission process.
                            </label>
                        </div>
                    </div>
                
                    <div class="row">
                        <div class="col-md-12 custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="clientPurpose" name="clientPurpose" {{#ifEquals
                                report.declaration.clientPurpose true}} checked {{/ifEquals}} required>
                            <label class="custom-control-label" for="clientPurpose">I have the authority to act on behalf of the
                                company.</label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="legalAdviseObtain" name="legalAdviseObtain"
                                {{#ifEquals report.declaration.legalAdviseObtain true}} checked {{/ifEquals}} required>
                            <label class="custom-control-label" for="legalAdviseObtain">
                                The Registered Agent has a legitimate interest in processing any personal data provided above to satisfy
                                the Entity’s compliance
                                with relevant BVI law. I further acknowledge that the processing of such personal data may include its
                                transfer to BVI competent
                                authorities, and that the Registered Agent’s processing of any personal data will be done in accordance
                                with Trident Trust Data
                                Privacy Policy, which I have read and understood. </label>
                        </div>
                    </div>
                
                </div>
            {{else}}
                <div class="pl-3 mt-2">
              
                    <div class="row">
                        <div class="col-md-12 custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="clientPurpose" name="clientPurpose" {{#ifEquals
                                report.declaration.clientPurpose true}} checked {{/ifEquals}} required>
                            <label class="custom-control-label" for="clientPurpose">
                                I have the authority to act on behalf of the company and further confirm that the details presented in this portal,
                                along with any document(s) submitted while completing this accounting return, have been provided to me by the Company’s
                                Director and to the best of the Company’s Directors knowledge and belief, are true and accurate.
                            </label>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-12 custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="legalAdviseObtain" name="legalAdviseObtain"
                                {{#ifEquals report.declaration.legalAdviseObtain true}} checked {{/ifEquals}} required>
                            <label class="custom-control-label" for="legalAdviseObtain">
                                The Registered Agent has a legitimate interest in processing any personal data provided above to satisfy the Entity’s
                                compliance with relevant BVI law. I further acknowledge that the processing of such personal data may include its
                                transfer to BVI competent authorities, and that the Registered Agent’s processing of any personal data will be done in
                                accordance with Trident Trust Data Privacy Policy, which I have read and understood.
                            </label>
                        </div>
                    </div>
                
                </div>

            {{/ifEquals}}


            <div class="row mt-3">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="declarationNameControl"> Name of person making the declaration*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <input type="text" name="declarationName" id="declarationNameControl" class="form-control"
                               required value="{{report.declaration.name}}">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="declarationRelationControl">Relation to entity*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <select name="declarationRelation" id="declarationRelationControl" class="form-control w-100" data-toggle="select2" required>
                            <option value="" hidden>Select an option</option>
                            <option {{#ifEquals report.declaration.relation "Director" }} selected
                            {{/ifEquals}} value="Director">Director</option>
                            <option {{#ifEquals report.declaration.relation "Sole Director" }} selected
                            {{/ifEquals}} value="Sole Director">Sole Director</option>
                            <option {{#ifEquals report.declaration.relation "Alternate Director" }}
                            selected {{/ifEquals}} value="Alternate Director">Alternate Director</option>
                            <option {{#ifEquals report.declaration.relation "Secretary" }} selected
                            {{/ifEquals}} value="Secretary">Secretary</option>
                            <option {{#ifEquals report.declaration.relation "Tax Advisor" }} selected
                            {{/ifEquals}} value="Tax Advisor">Tax Advisor</option>
                            <option {{#ifEquals report.declaration.relation "Legal Advisor" }} selected
                            {{/ifEquals}} value="Legal Advisor">Legal Advisor</option>
                            <option {{#ifEquals report.declaration.relation "Banker" }} selected
                            {{/ifEquals}} value="Banker">Banker</option>
                            <option {{#ifEquals report.declaration.relation "Authorized Agent" }}
                            selected {{/ifEquals}} value="Authorized Agent">Authorized Agent</option>
                            <option {{#ifEquals report.declaration.relation "Authorized Representative"
                            }} selected {{/ifEquals}} value="Authorized Representative">Authorized Representative
                            </option>
                            <option {{#ifEquals report.declaration.relation "Accountant" }} selected
                            {{/ifEquals}} value="Accountant">Accountant</option>
                            <option {{#ifEquals report.declaration.relation "Other" }} selected
                            {{/ifEquals}} value="Other">Other</option>
                        </select>
                    </div>
                </div>
            </div>
            <div id="declarationRelationOtherRow"  class="row {{#ifCond
                    report.declaration.relation "!=" "Other" }} hide-element {{else}} d-flex {{/ifCond}}">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="declarationRelationOtherControl">Please specify*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <input type="text" name="declarationRelationOther" id="declarationRelationOtherControl"
                               class="form-control" required value="{{report.declaration.relationOther}}">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="declarationPhoneNumberControl">Phone number*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <input type="text" name="declarationPhoneNumber" id="declarationPhoneNumberControl"
                               class="form-control" required value="{{report.declaration.phone}}">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="declarationEmailControl">Email*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <input type="text" name="declarationEmail" id="declarationEmailControl"
                               class="form-control" required value="{{report.declaration.email}}">
                    </div>
                </div>
                <div  id="previewExceptCompany" class="col-md-12 text-left {{#ifCond report.reportDetails.serviceType '===' 'self-service-complete'}} d-block {{else}} hide-element {{/ifCond}} {{#ifEquals report.reportDetails.isExemptCompany true}} d-block {{/ifEquals}} ">
                    <a href="/masterclients/{{masterClientCode}}/financial-reports/companies/{{companyCode}}/{{report._id}}/report.pdf"
                       class="btn solid royal-blue width-xl mb-2" target="_blank">Preview</a>
                </div>  
            </div>

            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-85" role="progressbar" aria-valuenow="6" id="declarationProgressBar"
                             aria-valuemin="0" aria-valuemax="7">6 of 7
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="/javascripts/libs/intlTelInput/intlTelInput.min.js"></script>
<link rel="stylesheet" href="/javascripts/libs/intlTelInput/intlTelInput.min.css">
<script type="text/javascript" src="/views-js/partials/financial-reports/declaration.js"></script>